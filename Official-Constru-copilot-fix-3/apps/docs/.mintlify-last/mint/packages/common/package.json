{"name": "@mintlify/common", "version": "1.0.410", "description": "Commonly shared code within Mintlify", "main": "./dist/index.js", "types": "./dist/index.d.ts", "sideEffects": false, "files": ["dist"], "type": "module", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "scripts": {"prepare": "npm run build", "build": "tsc --project tsconfig.build.json", "clean:build": "<PERSON><PERSON><PERSON> dist", "clean:all": "rimraf node_modules .eslintcache && yarn clean:build", "watch": "tsc --watch", "test": "vitest run", "type": "tsc --noEmit", "lint": "eslint . --cache", "format": "prettier . --write", "format:check": "prettier . --check"}, "author": "Mintlify, Inc.", "license": "ISC", "dependencies": {"@asyncapi/parser": "^3.4.0", "@mintlify/mdx": "^1.0.1", "@mintlify/models": "0.0.195", "@mintlify/openapi-parser": "^0.0.7", "@mintlify/validation": "0.1.390", "@sindresorhus/slugify": "^2.1.1", "acorn": "^8.11.2", "estree-util-to-js": "^2.0.0", "estree-walker": "^3.0.3", "gray-matter": "^4.0.3", "hast-util-from-html": "^2.0.3", "hast-util-to-html": "^9.0.4", "hast-util-to-text": "^4.0.2", "js-yaml": "^4.1.0", "lodash": "^4.17.21", "mdast": "^3.0.0", "mdast-util-from-markdown": "^2.0.2", "mdast-util-mdx": "^3.0.0", "mdast-util-mdx-jsx": "^3.1.3", "micromark-extension-mdx-jsx": "^3.0.1", "openapi-types": "^12.0.0", "remark": "^15.0.1", "remark-frontmatter": "^5.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "remark-mdx": "^3.1.0", "remark-stringify": "^11.0.0", "unified": "^11.0.5", "unist-builder": "^4.0.0", "unist-util-map": "^4.0.0", "unist-util-remove": "^4.0.0", "unist-util-remove-position": "^5.0.0", "unist-util-visit": "^5.0.0", "unist-util-visit-parents": "^6.0.1", "vfile": "^6.0.3"}, "devDependencies": {"@mintlify/eslint-config-typescript": "1.0.13", "@mintlify/openapi-types": "^0.0.0", "@mintlify/ts-config": "^2.0.2", "@tsconfig/recommended": "1.x", "@types/estree-jsx": "^1.0.5", "@types/hast": "^3.0.4", "@types/js-yaml": "^4.0.9", "@types/mdast": "^4.0.4", "@typescript-eslint/parser": "6.x", "eslint": "8.x", "hast": "^1.0.0", "openapi-types": "^12.0.0", "prettier": "^3.1.1", "rimraf": "^5.0.1", "typescript": "^5.5.3", "vitest": "^2.0.4"}}