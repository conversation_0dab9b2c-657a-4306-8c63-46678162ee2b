{"name": "@mintlify/scraping", "version": "4.0.266", "description": "Scrape documentation frameworks to Mintlify docs", "engines": {"node": ">=18.0.0"}, "author": "Mintlify, Inc.", "bugs": {"url": "https://github.com/mintlify/docs/issues"}, "license": "Elastic-2.0", "keywords": ["mintlify", "mint", "scraping"], "type": "module", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "main": "./bin/index.js", "types": "./bin/index.d.ts", "bin": {"mintlify-scrape": "bin/cli.js"}, "scripts": {"prepare": "npm run build", "build": "tsc --project tsconfig.build.json", "clean:build": "<PERSON><PERSON><PERSON> bin", "clean:all": "rimraf node_modules .eslintcache && yarn clean:build", "watch": "tsc --watch", "test": "vitest run", "type": "tsc --noEmit", "lint": "eslint . --cache", "format": "prettier . --write", "format:check": "prettier . --check"}, "dependencies": {"@mintlify/common": "1.0.410", "@mintlify/openapi-parser": "^0.0.7", "fs-extra": "^11.1.1", "hast-util-to-mdast": "^10.1.0", "js-yaml": "^4.1.0", "mdast-util-mdx-jsx": "^3.1.3", "neotraverse": "^0.6.18", "puppeteer": "^22.14.0", "rehype-parse": "^9.0.0", "remark-gfm": "^4.0.0", "remark-mdx": "^3.0.1", "remark-parse": "^11.0.0", "remark-stringify": "^11.0.0", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "yargs": "^17.6.0", "zod": "^3.20.6"}, "devDependencies": {"@mintlify/eslint-config-typescript": "1.0.13", "@mintlify/models": "0.0.195", "@mintlify/prettier-config": "1.0.4", "@mintlify/ts-config": "2.0.2", "@mintlify/validation": "0.1.390", "@trivago/prettier-plugin-sort-imports": "^4.2.1", "@tsconfig/recommended": "1.x", "@types/hast": "^3.0.4", "@types/mdast": "^4.0.4", "@types/node": "^18.7.13", "@types/yargs": "^17.0.13", "@typescript-eslint/eslint-plugin": "6.x", "@typescript-eslint/parser": "6.x", "eslint": "8.x", "openapi-types": "^12.0.0", "prettier": "^3.1.1", "rimraf": "^5.0.1", "typescript": "^5.5.3", "vitest": "^2.0.4"}}