{"name": "@mintlify/http-client", "version": "0.0.328", "type": "module", "description": "The Mintlify http client package", "engines": {"node": ">=18.0.0"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "sideEffects": false, "author": "Mintlify, Inc.", "bugs": {"url": "https://github.com/mintlify/docs/issues"}, "license": "Elastic-2.0", "keywords": ["mintlify", "mint", "client", "http"], "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "scripts": {"prepare": "npm run build", "build": "tsc --project tsconfig.build.json", "clean:build": "<PERSON><PERSON><PERSON> dist", "clean:all": "rimraf node_modules .eslintcache && yarn clean:build", "watch": "tsc --project tsconfig.build.json --watch", "lint": "eslint src --cache", "test": "vitest run", "type": "tsc --noEmit", "format": "prettier . --write", "format:check": "prettier . --check"}, "dependencies": {"@mintlify/models": "0.0.195", "@mintlify/validation": "0.1.390"}, "devDependencies": {"@mintlify/eslint-config-typescript": "1.0.13", "@mintlify/prettier-config": "1.0.4", "@mintlify/ts-config": "2.0.2", "@trivago/prettier-plugin-sort-imports": "^4.2.1", "@tsconfig/recommended": "1.x", "@types/node": "^18.7.13", "@types/node-fetch": "^2.6.11", "@typescript-eslint/parser": "6.x", "eslint": "8.x", "node-fetch": "^2.6.7", "prettier": "^3.1.1", "rimraf": "^5.0.1", "typescript": "^5.5.3", "vitest": "^2.0.4", "vitest-fetch-mock": "^0.3.0"}}