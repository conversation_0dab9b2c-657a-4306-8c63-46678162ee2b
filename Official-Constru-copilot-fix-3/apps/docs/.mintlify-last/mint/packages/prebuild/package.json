{"name": "@mintlify/prebuild", "type": "module", "version": "1.0.524", "description": "Helpful functions for Mintlify's prebuild step", "author": "Mintlify, Inc.", "repository": {"type": "git", "url": "https://github.com/mintlify/mint", "directory": "packages/prebuild"}, "bugs": {"url": "https://github.com/mintlify/mint/issues"}, "license": "Elastic-2.0", "keywords": ["mintlify", "mint", "prebuild"], "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "sideEffects": false, "scripts": {"prepare": "npm run build", "build": "tsc --project tsconfig.build.json", "clean:build": "<PERSON><PERSON><PERSON> dist", "clean:all": "rimraf node_modules .eslintcache && yarn clean:build", "watch": "tsc --project tsconfig.build.json --watch", "lint": "eslint . --cache", "test": "vitest run", "type": "tsc --noEmit", "format": "prettier . --write", "format:check": "prettier . --check"}, "dependencies": {"@mintlify/common": "1.0.410", "@mintlify/openapi-parser": "^0.0.7", "@mintlify/scraping": "4.0.266", "@mintlify/validation": "0.1.390", "axios": "^1.6.2", "chalk": "^5.3.0", "favicons": "^7.2.0", "fs-extra": "^11.1.0", "gray-matter": "^4.0.3", "js-yaml": "^4.1.0", "mdast": "^3.0.0", "openapi-types": "^12.0.0", "unist-util-visit": "^4.1.1"}, "devDependencies": {"@mintlify/eslint-config-typescript": "1.0.13", "@mintlify/models": "0.0.195", "@mintlify/prettier-config": "1.0.4", "@mintlify/ts-config": "2.0.2", "@trivago/prettier-plugin-sort-imports": "^4.2.1", "@tsconfig/recommended": "1.x", "@types/node": "^18.15.5", "@typescript-eslint/parser": "6.x", "eslint": "8.x", "prettier": "^3.1.1", "rimraf": "^5.0.1", "typescript": "^5.5.3", "vitest": "^2.0.4"}}