{"name": "mintlify-monorepo", "description": "Mintlify monorepo", "private": true, "author": "Mintlify, Inc.", "repository": {"type": "git", "url": "https://github.com/mintlify/mint"}, "bugs": {"url": "https://github.com/mintlify/mint/issues"}, "license": "Elastic-2.0", "workspaces": {"packages": ["client", "packages/*", "apps/*"]}, "scripts": {"prepare": "husky install", "build": "yarn workspaces foreach --topological-dev --exclude @mintlify/client --exclude @mintlify/dashboard -v run build", "build:vercel": "yarn workspaces foreach --topological-dev --exclude @mintlify/dashboard -v run build", "build:client": "yarn workspaces foreach --topological-dev --exclude @mintlify/dashboard --exclude mint -v run build", "build:dashboard": "yarn workspaces foreach --topological-dev --exclude @mintlify/client -v run build", "build:storybook:client": "yarn workspace @mintlify/client build:storybook", "clean:build": "yarn workspaces foreach --topological-dev -v run clean:build", "clean:all": "yarn workspaces foreach --topological-dev -v run clean:all && rimraf .yarn/install-state.gz node_modules && echo Run `yarn` to reinstall dependencies", "test": "yarn workspaces foreach -v -p run test", "type": "yarn workspaces foreach -v -p run type", "format": "prettier \"./*.{json,md}\" --write && yarn workspaces foreach -v -p run format", "format:check": "prettier \"./*.{json,md}\" --check && yarn workspaces foreach -v -p run format:check", "lint": "yarn workspaces foreach -v -p run lint", "lint:compare": "tsx --max-old-space-size=6144 scripts/lint-compare.mts", "depcheck": "tsx scripts/depcheck.mts"}, "dependencies": {"@mintlify/prettier-config": "1.0.4", "@mintlify/ts-config": "2.0.2", "@trivago/prettier-plugin-sort-imports": "^4.2.1", "@tsconfig/recommended": "1.x", "@types/formidable": "^3.4.5", "@types/node": "^20.4.5", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/parser": "6.x", "depcheck": "^1.4.5", "eslint": "8.x", "formidable": "^3.5.2", "husky": "8.0.3", "lerna": "^8.0.0", "lint-staged": "^13.2.0", "prettier": "^3.1.1", "rimraf": "^5.0.1", "tsx": "^4.16.2", "typescript": "^5.5.3"}, "packageManager": "yarn@3.4.1", "lint-staged": {"*.+(ts|tsx)": "eslint --cache --fix --max-warnings 0", "*.{css,js,ts,tsx,md,mdx}": "prettier --write"}, "devDependencies": {"eslint-plugin-storybook": "^0.12.0"}}