import { z } from "zod";

// Request schemas
export const getBimModelsSchema = z.object({
  pageSize: z.coerce.number().max(100).default(50).optional(),
});

export const getBimModelSchema = z.object({
  id: z.string().uuid(),
});

export const getModelAnnotationsSchema = z.object({
  modelId: z.string().uuid(),
});

export const createModelAnnotationSchema = z.object({
  modelId: z.string().uuid(),
  title: z.string().min(1).max(255),
  description: z.string().optional(),
  type: z.enum(["comment", "issue", "rfi"]).default("comment"),
  position: z
    .object({
      x: z.number(),
      y: z.number(),
      z: z.number(),
    })
    .optional(),
});

// Response schemas
export const bimModelResponseSchema = z.object({
  id: z.string(),
  documentId: z.string(),
  name: z.string(),
  fileFormat: z.string(),
  processingStatus: z.string(),
  metadata: z.any().nullable(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export const bimModelsResponseSchema = z.object({
  data: z.array(bimModelResponseSchema),
});

export const modelAnnotationResponseSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().nullable(),
  type: z.string(),
  status: z.string(),
  position: z.any().nullable(),
  createdAt: z.string(),
  user: z.object({
    id: z.string(),
    name: z.string().nullable(),
    email: z.string(),
  }),
});

export const modelAnnotationsResponseSchema = z.object({
  data: z.array(modelAnnotationResponseSchema),
});

export const createAnnotationResponseSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().nullable(),
  type: z.string(),
  status: z.string(),
  position: z.any().nullable(),
  createdAt: z.string(),
});