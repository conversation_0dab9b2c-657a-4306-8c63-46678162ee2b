import { TRPCError } from "@trpc/server";
import type { MiddlewareFunction } from "@trpc/server/unstable-core-do-not-import";
import type { Context } from "../init";

/**
 * Enhanced error handling middleware for TRPC
 * Provides consistent error responses and logging
 */
export const errorHandlingMiddleware: MiddlewareFunction<
  {
    ctx: Context;
  },
  {
    ctx: Context;
  },
  unknown
> = async ({ ctx, next, path, type }) => {
  const start = Date.now();
  
  try {
    const result = await next();
    
    // Log successful operations in development
    if (process.env.NODE_ENV === 'development') {
      const duration = Date.now() - start;
      console.log(`✅ ${type} ${path} - ${duration}ms`);
    }
    
    return result;
  } catch (error) {
    const duration = Date.now() - start;
    
    // Enhanced error logging
    console.error(`❌ TRPC Error in ${type} ${path} (${duration}ms):`, {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      path,
      type,
      userId: ctx.user?.id,
      teamId: ctx.user?.teamId,
    });

    // Handle different types of errors
    if (error instanceof TRPCError) {
      // Re-throw TRPC errors as-is
      throw error;
    }

    // Handle database connection errors
    if (error instanceof Error) {
      if (error.message.includes('connect') || error.message.includes('connection')) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Database connection failed. Please try again.',
          cause: error,
        });
      }

      // Handle authentication/authorization errors
      if (error.message.includes('auth') || error.message.includes('unauthorized')) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'Authentication required.',
          cause: error,
        });
      }

      // Handle validation errors
      if (error.message.includes('validation') || error.message.includes('invalid')) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Invalid input data.',
          cause: error,
        });
      }

      // Handle rate limiting
      if (error.message.includes('rate') || error.message.includes('limit')) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: 'Rate limit exceeded. Please try again later.',
          cause: error,
        });
      }

      // Handle missing resources
      if (error.message.includes('not found') || error.message.includes('does not exist')) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Resource not found.',
          cause: error,
        });
      }

      // Handle timeout errors
      if (error.message.includes('timeout') || error.message.includes('timed out')) {
        throw new TRPCError({
          code: 'TIMEOUT',
          message: 'Request timed out. Please try again.',
          cause: error,
        });
      }

      // Handle SQL errors (from Drizzle/Postgres)
      if (error.message.includes('relation') || error.message.includes('column') || error.message.includes('function')) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Database query failed. Please contact support if this persists.',
          cause: error,
        });
      }
    }

    // Generic error fallback
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: 'An unexpected error occurred. Please try again.',
      cause: error,
    });
  }
};

/**
 * Database operation wrapper with automatic error handling
 */
export async function withDatabaseErrorHandling<T>(
  operation: () => Promise<T>,
  operationName: string = 'database operation'
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    console.error(`Database error in ${operationName}:`, error);
    
    if (error instanceof Error) {
      // Handle specific database errors
      if (error.message.includes('connect ECONNREFUSED')) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Database is temporarily unavailable.',
        });
      }
      
      if (error.message.includes('timeout')) {
        throw new TRPCError({
          code: 'TIMEOUT',
          message: 'Database query timed out.',
        });
      }
      
      if (error.message.includes('constraint')) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'Data constraint violation.',
        });
      }
    }
    
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: `Failed to execute ${operationName}.`,
      cause: error,
    });
  }
}

/**
 * Environment variable validation wrapper
 */
export function requireEnvVar(name: string, fallback?: string): string {
  const value = process.env[name];
  if (!value) {
    if (fallback !== undefined) {
      console.warn(`Environment variable ${name} not found, using fallback: ${fallback}`);
      return fallback;
    }
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: `Server configuration error: ${name} not configured.`,
    });
  }
  return value;
}

/**
 * Team permission check wrapper
 */
export function requireTeamAccess(userTeamId: string | null, requiredTeamId: string) {
  if (!userTeamId || userTeamId !== requiredTeamId) {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'Access denied. You do not have permission to access this team\'s data.',
    });
  }
}