import type { Database } from "@api/db";
import { usersOnTeam } from "@api/db/schema";
import type { Session } from "@api/utils/auth";
import { TRPCError } from "@trpc/server";
import { LRUCache } from "lru-cache";

// In-memory cache to check if a user has access to a team
// Note: This cache is per server instance, and we typically run 1 instance per region.
// Otherwise, we would need to share this state with Redis or a similar external store.
const cache = new LRUCache<string, boolean>({
  max: 5_000, // up to 5k entries (adjust based on memory)
  ttl: 1000 * 60 * 30, // 30 minutes in milliseconds
});

export const withTeamPermission = async <TReturn>(opts: {
  ctx: {
    session?: Session | null;
    db: Database;
  };
  next: (opts: {
    ctx: {
      session?: Session | null;
      db: Database;
      teamId: string;
    };
  }) => Promise<TReturn>;
}) => {
  const { ctx, next } = opts;

  const userId = ctx.session?.user?.id;

  if (!userId) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "No permission to access this team",
    });
  }

  // First, get the user's primary team
  const user = await ctx.db.query.users.findFirst({
    columns: {
      id: true,
      teamId: true,
    },
    where: (users, { eq }) => eq(users.id, userId),
  });

  if (!user) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "User not found",
    });
  }

  // If user has no team, they need to complete setup
  if (!user.teamId) {
    // Return a specific error that the client can handle
    throw new TRPCError({
      code: "PRECONDITION_FAILED",
      message: "NO_TEAM_ASSIGNED",
    });
  }

  const teamId = user.teamId;
  const cacheKey = `user:${userId}:team:${teamId}`;
  let hasAccess = cache.get(cacheKey);

  if (hasAccess === undefined) {
    // Check if user is a member of this team
    const membership = await ctx.db.query.usersOnTeam.findFirst({
      columns: {
        id: true,
      },
      where: (usersOnTeam, { and, eq }) =>
        and(eq(usersOnTeam.userId, userId), eq(usersOnTeam.teamId, teamId)),
    });

    // If user has a teamId but no usersOnTeam entry, create it automatically
    if (!membership) {
      try {
        await ctx.db.insert(usersOnTeam).values({
          userId,
          teamId,
          role: "member", // Default role
        });
        hasAccess = true;
        console.log(`Auto-created usersOnTeam entry for user ${userId} in team ${teamId}`);
      } catch (error) {
        console.warn(`Failed to auto-create usersOnTeam entry: ${error}`);
        hasAccess = false;
      }
    } else {
      hasAccess = true;
    }
    
    cache.set(cacheKey, hasAccess);
  }

  if (!hasAccess) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "No permission to access this team",
    });
  }

  return next({
    ctx: {
      session: ctx.session,
      teamId,
      db: ctx.db,
    },
  });
};
