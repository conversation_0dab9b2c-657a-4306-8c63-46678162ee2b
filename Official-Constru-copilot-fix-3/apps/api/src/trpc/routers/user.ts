import { getUserInvites } from "@api/db/queries/user-invites";
import { deleteUser, getUserById, updateUser } from "@api/db/queries/users";
import { updateUserSchema } from "@api/schemas/users";
import { resend } from "@api/services/resend";
import { createAdminClient } from "@api/services/supabase";
import { createTRPCRouter, authenticatedProcedure, protectedProcedure } from "@api/trpc/init";

export const userRouter = createTRPCRouter({
  me: authenticatedProcedure.query(async ({ ctx: { db, session } }) => {
    return getUserById(db, session.user.id);
  }),

  update: authenticatedProcedure
    .input(updateUserSchema)
    .mutation(async ({ ctx: { db, session }, input }) => {
      // Update user in database
      const updatedUser = await updateUser(db, {
        id: session.user.id,
        ...input,
      });

      // If fullName is provided, also update Supabase user metadata
      if (input.fullName) {
        const adminClient = createAdminClient();
        await adminClient.auth.admin.updateUserById(session.user.id, {
          user_metadata: {
            full_name: input.fullName,
          },
        });
      }

      return updatedUser;
    }),

  delete: protectedProcedure.mutation(async ({ ctx: { db, session } }) => {
    const adminClient = createAdminClient();
    const [data] = await Promise.all([
      deleteUser(db, session.user.id),
      adminClient.auth.admin.deleteUser(session.user.id),
      resend.contacts.remove({
        email: session.user.email!,
        audienceId: process.env.RESEND_AUDIENCE_ID!,
      }),
    ]);

    return data;
  }),

  invites: authenticatedProcedure.query(async ({ ctx: { db, session } }) => {
    if (!session.user.email) {
      return [];
    }

    return getUserInvites(db, session.user.email);
  }),
});
