import {
  deleteApi<PERSON><PERSON>,
  getApi<PERSON><PERSON>sByTeam,
  upsertApi<PERSON><PERSON>,
} from "@api/db/queries/api-keys";
import { deleteApiKeySchema, upsertApiKeySchema } from "@api/schemas/api-keys";
import { resend } from "@api/services/resend";
import { createTRPCRouter, protectedProcedure } from "@api/trpc/init";
import { logger } from "@api/utils/logger";
import { ApiKeyCreatedEmail } from "@constru/email/emails/api-key-created";

export const apiKeysRouter = createTRPCRouter({
  get: protectedProcedure.query(async ({ ctx: { db, teamId } }) => {
    return getApiKeysByTeam(db, teamId!);
  }),

  upsert: protectedProcedure
    .input(upsertApiKeySchema)
    .mutation(async ({ ctx: { db, teamId, session, geo }, input }) => {
      const { data, key } = await upsertApi<PERSON>ey(db, {
        teamId: teamId!,
        userId: session.user.id,
        ...input,
      });

      if (data) {
        try {
          // We don't need to await this, it will be sent in the background
          resend.emails.send({
            from: "Construbot <<EMAIL>>",
            to: session.user.email!,
            subject: "New API Key Created",
            react: ApiKeyCreatedEmail({
              fullName: session.user.full_name!,
              keyName: input.name,
              createdAt: data.createdAt,
              email: session.user.email!,
              ip: geo.ip!,
            }),
          });
        } catch (error) {
          logger.error(error);
        }
      }

      return {
        key,
        data,
      };
    }),

  delete: protectedProcedure
    .input(deleteApiKeySchema)
    .mutation(async ({ ctx: { db, teamId }, input }) => {
      return deleteApiKey(db, {
        teamId: teamId!,
        ...input,
      });
    }),
});
