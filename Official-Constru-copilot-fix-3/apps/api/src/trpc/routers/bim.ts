import { z } from "zod";
import {
  getBimModels,
  getBimModelById,
  getModelAnnotations,
  createModelAnnotation,
} from "@api/db/queries/bim";
import { createTRPCRouter, protectedProcedure } from "@api/trpc/init";

export const bimRouter = createTRPCRouter({
  list: protectedProcedure
    .input(
      z.object({
        pageSize: z.number().max(100).default(50).optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const result = await getBimModels(ctx.db, {
        teamId: ctx.teamId!,
        pageSize: input.pageSize,
      });

      return { data: result };
    }),

  get: protectedProcedure
    .input(z.object({ id: z.string().uuid() }))
    .query(async ({ ctx, input }) => {
      const result = await getBimModelById(ctx.db, {
        id: input.id,
        teamId: ctx.teamId!,
      });

      if (!result) {
        throw new Error("BIM model not found");
      }

      return result;
    }),

  getAnnotations: protectedProcedure
    .input(z.object({ bimModelId: z.string().uuid() }))
    .query(async ({ ctx, input }) => {
      const result = await getModelAnnotations(ctx.db, {
        bimModelId: input.bimModelId,
        teamId: ctx.teamId!,
      });

      return { data: result };
    }),

  createAnnotation: protectedProcedure
    .input(
      z.object({
        bimModelId: z.string().uuid(),
        title: z.string().min(1).max(255),
        description: z.string().optional(),
        type: z.enum(["comment", "issue", "rfi"]).default("comment"),
        position: z
          .object({
            x: z.number(),
            y: z.number(),
            z: z.number(),
          })
          .optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const result = await createModelAnnotation(ctx.db, {
        bimModelId: input.bimModelId,
        teamId: ctx.teamId!,
        userId: ctx.session.user.id,
        title: input.title,
        description: input.description,
        type: input.type,
        position: input.position,
      });

      return result;
    }),
});