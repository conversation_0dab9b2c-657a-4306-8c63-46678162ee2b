import {
  createProjectPhase,
  getProjectPhases,
  createProjectTask,
  getProjectTasks,
  updateTaskProgress,
  assignTeamMember,
  getProjectTeam,
  createProjectResource,
  getProjectResources,
  getProjectDashboardData,
  createProjectCommunication,
  getProjectCommunications,
  checkProjectPermission,
  type CreateProjectPhaseParams,
  type CreateProjectTaskParams,
  type AssignTeamMemberParams,
  type CreateResourceParams,
  type CreateCommunicationParams
} from "@api/db/queries/project-management";
import { createTRPCRouter, protectedProcedure } from "@api/trpc/init";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

// Project Phases Schemas
const createPhaseSchema = z.object({
  projectId: z.string().uuid(),
  name: z.string().min(1),
  description: z.string().optional(),
  phaseOrder: z.number().int(),
  estimatedStart: z.string().optional(),
  estimatedEnd: z.string().optional(),
  dependencies: z.array(z.string().uuid()).optional()
});

const getProjectPhasesSchema = z.object({
  projectId: z.string().uuid()
});

// Project Tasks Schemas
const createTaskSchema = z.object({
  projectId: z.string().uuid(),
  phaseId: z.string().uuid().optional(),
  parentTaskId: z.string().uuid().optional(),
  name: z.string().min(1),
  description: z.string().optional(),
  taskOrder: z.number().int().optional(),
  priority: z.enum(['low', 'medium', 'high', 'critical']).optional(),
  estimatedHours: z.string().optional(),
  estimatedCost: z.string().optional(),
  estimatedStart: z.string().optional(),
  estimatedEnd: z.string().optional(),
  assignedTo: z.string().uuid().optional(),
  dependencies: z.array(z.string().uuid()).optional()
});

const getProjectTasksSchema = z.object({
  projectId: z.string().uuid(),
  phaseId: z.string().uuid().optional(),
  assignedTo: z.string().uuid().optional(),
  status: z.enum(['not_started', 'in_progress', 'on_hold', 'completed', 'cancelled', 'blocked']).optional()
});

const updateTaskProgressSchema = z.object({
  taskId: z.string().uuid(),
  status: z.enum(['not_started', 'in_progress', 'on_hold', 'completed', 'cancelled', 'blocked']).optional(),
  completionPercentage: z.string().optional(),
  actualHours: z.string().optional(),
  actualCost: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional()
});

// Team Management Schemas
const assignTeamMemberSchema = z.object({
  projectId: z.string().uuid(),
  userId: z.string().uuid(),
  role: z.enum(['project_manager', 'foreman', 'worker', 'client', 'supervisor', 'admin', 'subcontractor']),
  hourlyRate: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  permissions: z.any().optional()
});

const getProjectTeamSchema = z.object({
  projectId: z.string().uuid()
});

// Resources Schemas
const createResourceSchema = z.object({
  projectId: z.string().uuid(),
  taskId: z.string().uuid().optional(),
  resourceType: z.enum(['labor', 'equipment', 'material', 'subcontractor', 'permit']),
  name: z.string().min(1),
  description: z.string().optional(),
  quantity: z.string(),
  unit: z.string().optional(),
  unitCost: z.string().optional(),
  totalCost: z.string().optional(),
  vendorContact: z.string().optional(),
  availabilityStart: z.string().optional(),
  availabilityEnd: z.string().optional()
});

const getProjectResourcesSchema = z.object({
  projectId: z.string().uuid(),
  resourceType: z.string().optional()
});

// Dashboard Schemas
const getProjectDashboardSchema = z.object({
  projectIds: z.array(z.string().uuid()).optional(),
  status: z.enum(['in_progress', 'completed']).optional(),
  limit: z.number().int().positive().optional()
});

// Communications Schemas
const createCommunicationSchema = z.object({
  projectId: z.string().uuid(),
  recipients: z.array(z.string().uuid()).min(1),
  communicationType: z.string().optional(),
  subject: z.string().optional(),
  content: z.string().min(1),
  priority: z.enum(['low', 'medium', 'high', 'critical']).optional(),
  attachments: z.array(z.string().uuid()).optional(),
  threadId: z.string().uuid().optional()
});

const getProjectCommunicationsSchema = z.object({
  projectId: z.string().uuid()
});

// Role-based permission middleware
const createProjectPermissionProcedure = (requiredRole?: string) =>
  protectedProcedure.use(async ({ ctx, next, input }) => {
    const { db, session } = ctx;
    
    // Extract projectId from input
    const projectId = (input as any)?.projectId;
    
    if (!projectId) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "Project ID is required"
      });
    }

    // Check project permission
    const permission = await checkProjectPermission(
      db,
      session.user.id,
      projectId,
      requiredRole
    );

    if (!permission.hasAccess) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: `Insufficient permissions. Required role: ${requiredRole || 'team member'}`
      });
    }

    return next({
      ctx: {
        ...ctx,
        userRole: permission.role,
        userPermissions: permission.permissions
      }
    });
  });

// Create procedures with different permission levels
const teamMemberProcedure = createProjectPermissionProcedure();
const foremanProcedure = createProjectPermissionProcedure('foreman');
const projectManagerProcedure = createProjectPermissionProcedure('project_manager');

export const projectManagementRouter = createTRPCRouter({
  // Project Phases
  phases: createTRPCRouter({
    create: projectManagerProcedure
      .input(createPhaseSchema)
      .mutation(async ({ input, ctx: { db } }) => {
        return createProjectPhase(db, input);
      }),

    get: teamMemberProcedure
      .input(getProjectPhasesSchema)
      .query(async ({ input, ctx: { db } }) => {
        return getProjectPhases(db, input.projectId);
      })
  }),

  // Project Tasks
  tasks: createTRPCRouter({
    create: foremanProcedure
      .input(createTaskSchema)
      .mutation(async ({ input, ctx: { db, session } }) => {
        return createProjectTask(db, input, session.user.id);
      }),

    get: teamMemberProcedure
      .input(getProjectTasksSchema)
      .query(async ({ input, ctx: { db } }) => {
        return getProjectTasks(db, input);
      }),

    updateProgress: teamMemberProcedure
      .input(updateTaskProgressSchema)
      .mutation(async ({ input, ctx: { db } }) => {
        const { taskId, ...updateData } = input;
        return updateTaskProgress(db, taskId, updateData);
      })
  }),

  // Team Management
  team: createTRPCRouter({
    assign: projectManagerProcedure
      .input(assignTeamMemberSchema)
      .mutation(async ({ input, ctx: { db } }) => {
        const { role, ...rest } = input;
        return assignTeamMember(db, {
          ...rest,
          permissions: {
            role,
            ...(input.permissions || {})
          }
        });
      }),

    get: teamMemberProcedure
      .input(getProjectTeamSchema)
      .query(async ({ input, ctx: { db } }) => {
        return getProjectTeam(db, input.projectId);
      })
  }),

  // Resources
  resources: createTRPCRouter({
    create: foremanProcedure
      .input(createResourceSchema)
      .mutation(async ({ input, ctx: { db } }) => {
        return createProjectResource(db, input);
      }),

    get: teamMemberProcedure
      .input(getProjectResourcesSchema)
      .query(async ({ input, ctx: { db } }) => {
        return getProjectResources(db, input.projectId, input.resourceType as 'labor' | 'equipment' | 'material' | 'subcontractor' | 'permit' | undefined);
      })
  }),

  // Dashboard
  dashboard: protectedProcedure
    .input(getProjectDashboardSchema.optional())
    .query(async ({ input, ctx: { db, teamId, session } }) => {
      return getProjectDashboardData(db, {
        teamId: teamId!,
        userId: session.user.id,
        projectIds: input?.projectIds,
        status: input?.status,
        limit: input?.limit
      });
    }),

  // Communications
  communications: createTRPCRouter({
    create: teamMemberProcedure
      .input(createCommunicationSchema)
      .mutation(async ({ input, ctx: { db, session } }) => {
        return createProjectCommunication(db, {
          ...input,
          senderId: session.user.id
        });
      }),

    get: teamMemberProcedure
      .input(getProjectCommunicationsSchema)
      .query(async ({ input, ctx: { db, session } }) => {
        return getProjectCommunications(db, input.projectId, session.user.id);
      })
  }),

  // User Role and Permissions
  permissions: createTRPCRouter({
    check: protectedProcedure
      .input(z.object({
        projectId: z.string().uuid(),
        requiredRole: z.string().optional()
      }))
      .query(async ({ input, ctx: { db, session } }) => {
        return checkProjectPermission(
          db,
          session.user.id,
          input.projectId,
          input.requiredRole
        );
      })
  })
});