import {
  getBimModels,
  getBimModelById,
  getModelAnnotations,
  createModelAnnotation,
} from "@api/db/queries/bim";
import type { Context } from "@api/rest/types";
import {
  getBimModelsSchema,
  getBimModelSchema,
  getModelAnnotationsSchema,
  createModelAnnotationSchema,
  bimModelsResponseSchema,
  bimModelResponseSchema,
  modelAnnotationsResponseSchema,
  createAnnotationResponseSchema,
} from "@api/schemas/bim";
import { validateResponse } from "@api/utils/validate-response";
import { OpenAPIHono, createRoute } from "@hono/zod-openapi";
import { withRequiredScope } from "../middleware";
import { z } from "zod";

const app = new OpenAPIHono<Context>();

app.openapi(
  createRoute({
    method: "get",
    path: "/",
    summary: "List BIM models",
    operationId: "listBimModels",
    description: "Retrieve a list of BIM models for the authenticated team.",
    tags: ["BIM"],
    request: {
      query: getBimModelsSchema,
    },
    responses: {
      200: {
        description: "List of BIM models",
        content: {
          "application/json": {
            schema: bimModelsResponseSchema,
          },
        },
      },
    },
    middleware: [withRequiredScope("documents.read")],
  }),
  async (c) => {
    const db = c.get("db");
    const teamId = c.get("teamId");
    const { pageSize } = c.req.valid("query");

    const result = await getBimModels(db, { teamId, pageSize });

    return c.json(validateResponse({ data: result }, bimModelsResponseSchema));
  },
);

app.openapi(
  createRoute({
    method: "get",
    path: "/{id}",
    summary: "Get BIM model",
    operationId: "getBimModel",
    description: "Retrieve a BIM model by ID.",
    tags: ["BIM"],
    request: {
      params: getBimModelSchema,
    },
    responses: {
      200: {
        description: "BIM model details",
        content: {
          "application/json": {
            schema: bimModelResponseSchema,
          },
        },
      },
    },
    middleware: [withRequiredScope("documents.read")],
  }),
  async (c) => {
    const db = c.get("db");
    const teamId = c.get("teamId");
    const { id } = c.req.valid("param");

    const result = await getBimModelById(db, { id, teamId });

    return c.json(validateResponse(result, bimModelResponseSchema));
  },
);

app.openapi(
  createRoute({
    method: "get",
    path: "/{modelId}/annotations",
    summary: "Get model annotations",
    operationId: "getModelAnnotations",
    description: "Retrieve annotations for a BIM model.",
    tags: ["BIM"],
    request: {
      params: getModelAnnotationsSchema,
    },
    responses: {
      200: {
        description: "Model annotations",
        content: {
          "application/json": {
            schema: modelAnnotationsResponseSchema,
          },
        },
      },
    },
    middleware: [withRequiredScope("documents.read")],
  }),
  async (c) => {
    const db = c.get("db");
    const teamId = c.get("teamId");
    const { modelId } = c.req.valid("param");

    const result = await getModelAnnotations(db, {
      bimModelId: modelId,
      teamId,
    });

    return c.json(
      validateResponse({ data: result }, modelAnnotationsResponseSchema),
    );
  },
);

app.openapi(
  createRoute({
    method: "post",
    path: "/{modelId}/annotations",
    summary: "Create model annotation",
    operationId: "createModelAnnotation",
    description: "Create a new annotation for a BIM model.",
    tags: ["BIM"],
    request: {
      params: z.object({ modelId: z.string().uuid() }),
      body: {
        content: {
          "application/json": {
            schema: createModelAnnotationSchema.omit({ modelId: true }),
          },
        },
      },
    },
    responses: {
      201: {
        description: "Annotation created",
        content: {
          "application/json": {
            schema: createAnnotationResponseSchema,
          },
        },
      },
    },
    middleware: [withRequiredScope("documents.write")],
  }),
  async (c) => {
    const db = c.get("db");
    const teamId = c.get("teamId");
    const session = c.get("session");
    const { modelId } = c.req.valid("param");
    const body = c.req.valid("json");

    const result = await createModelAnnotation(db, {
      bimModelId: modelId,
      teamId,
      userId: session.user.id,
      title: body.title,
      description: body.description,
      type: body.type,
      position: body.position,
    });

    return c.json(
      validateResponse(result, createAnnotationResponseSchema),
      201,
    );
  },
);

export default app;