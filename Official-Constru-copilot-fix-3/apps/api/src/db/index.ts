import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { withReplicas } from "./replicas";
import * as schema from "./schema";

// Validate required environment variables
if (!process.env.DATABASE_PRIMARY_URL) {
  throw new Error("DATABASE_PRIMARY_URL is required");
}

const primaryPool = postgres(process.env.DATABASE_PRIMARY_URL!, {
  prepare: false,
  max: 20,
  idle_timeout: 30,
  connect_timeout: 10,
});

// Create replica pools only if URLs are provided
const fraPool = process.env.DATABASE_FRA_URL 
  ? postgres(process.env.DATABASE_FRA_URL, { prepare: false, max: 20, idle_timeout: 30, connect_timeout: 10 })
  : null;
const sjcPool = process.env.DATABASE_SJC_URL 
  ? postgres(process.env.DATABASE_SJC_URL, { prepare: false, max: 20, idle_timeout: 30, connect_timeout: 10 })
  : null;
const iadPool = process.env.DATABASE_IAD_URL 
  ? postgres(process.env.DATABASE_IAD_URL, { prepare: false, max: 20, idle_timeout: 30, connect_timeout: 10 })
  : null;

export const primaryDb = drizzle(primaryPool, {
  schema,
  casing: "snake_case",
});

const getReplicaIndexForRegion = () => {
  switch (process.env.FLY_REGION) {
    case "fra":
      return 0;
    case "iad":
      return 1;
    case "sjc":
      return 2;
    default:
      return 0;
  }
};

export const connectDb = async () => {
  // If no replica databases are configured, create a wrapper with just primary
  const availableReplicas = [fraPool, iadPool, sjcPool].filter(Boolean);
  
  if (availableReplicas.length === 0) {
    console.warn("No replica databases configured, using primary database only");
    // Create a wrapper with executeOnReplica pointing to primary
    return withReplicas(
      primaryDb,
      [primaryDb], // Pass primary as the only replica
      () => primaryDb,
    );
  }

  const replicaIndex = getReplicaIndexForRegion();
  
  // Create drizzle instances only for available pools
  const replicaDbs = [];
  if (fraPool) replicaDbs.push(drizzle(fraPool, { schema, casing: "snake_case" }));
  if (iadPool) replicaDbs.push(drizzle(iadPool, { schema, casing: "snake_case" }));
  if (sjcPool) replicaDbs.push(drizzle(sjcPool, { schema, casing: "snake_case" }));

  return withReplicas(
    primaryDb,
    replicaDbs as [typeof primaryDb, ...typeof primaryDb[]],
    (replicas) => replicas[Math.min(replicaIndex, replicas.length - 1)] || replicas[0]!,
  );
};

export type Database = Awaited<ReturnType<typeof connectDb>>;

export type DatabaseWithPrimary = Database & {
  $primary?: Database;
  usePrimaryOnly?: () => Database;
};
