import { sql } from "drizzle-orm";
import type { Database } from "..";

export type GlobalSearchReturnType = {
  id: string;
  type: string;
  title: string;
  relevance: number;
  created_at: string;
  data: any;
};

export type GlobalSemanticSearchParams = {
  teamId: string;
  searchTerm: string;
  itemsPerTableLimit: number;
  language?: string;
  types?: string[];
  amount?: number;
  amountMin?: number;
  amountMax?: number;
  status?: string;
  currency?: string;
  startDate?: string;
  endDate?: string;
  dueDateStart?: string;
  dueDateEnd?: string;
};

/**
 * Safe version of globalSemanticSearchQuery that handles missing database functions
 */
export async function globalSemanticSearchQuerySafe(
  db: Database,
  params: GlobalSemanticSearchParams,
): Promise<GlobalSearchReturnType[]> {
  try {
    // Prepare types as a Postgres text[] array, or null if not present
    const typesParam =
      Array.isArray(params.types) && params.types.length > 0
        ? sql`ARRAY[${sql.join(
            params.types.map((t) => sql`${t}`),
            sql`, `,
          )}]`
        : null;

    const result: GlobalSearchReturnType[] = await db.executeOnReplica(
      sql`SELECT * FROM global_semantic_search(
          ${params.teamId ?? null},
          ${params.searchTerm ?? null},
          ${params.startDate ?? null},
          ${params.endDate ?? null},
          ${typesParam ?? null},
          ${params.amount ?? null},
          ${params.amountMin ?? null},
          ${params.amountMax ?? null},
          ${params.status ?? null},
          ${params.currency ?? null},
          ${params.language ?? null},
          ${params.dueDateStart ?? null},
          ${params.dueDateEnd ?? null},
          ${params.itemsPerTableLimit},
          ${params.itemsPerTableLimit}
        )`,
    );

    return result;
  } catch (error) {
    console.error("Error in globalSemanticSearchQuery:", error);
    // Return empty results if the function doesn't exist
    return [];
  }
}

type GlobalSearchParams = {
  teamId: string;
  searchTerm?: string;
  limit?: number;
  itemsPerTableLimit?: number;
  language?: string;
  relevanceThreshold?: number;
};

/**
 * Safe version of globalSearchQuery that handles missing database functions
 */
export async function globalSearchQuerySafe(
  db: Database,
  params: GlobalSearchParams,
): Promise<GlobalSearchReturnType[]> {
  try {
    const result: GlobalSearchReturnType[] = await db.executeOnReplica(
      sql`SELECT * FROM global_search(
          ${params.searchTerm ?? null},
          ${params.teamId ?? null},
          ${params.language ?? "english"},
          ${params.limit ?? null},
          ${params.itemsPerTableLimit ?? null},
          ${params.relevanceThreshold ?? null}
        )`,
    );

    return result;
  } catch (error) {
    console.error("Error in globalSearchQuery:", error);
    // Return empty results if the function doesn't exist
    return [];
  }
}