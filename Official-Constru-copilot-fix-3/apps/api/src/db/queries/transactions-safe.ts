import type { Database } from "@api/db";
import type { GetTransactionsParams } from "./transactions";
import { getTransactions as getTransactionsUnsafe } from "./transactions";

/**
 * Safe wrapper for getTransactions that handles errors gracefully
 */
export async function getTransactionsSafe(
  db: Database,
  params: GetTransactionsParams,
) {
  try {
    return await getTransactionsUnsafe(db, params);
  } catch (error) {
    console.error("Error fetching transactions:", error);
    
    // Return empty results with pagination info
    return {
      data: [],
      meta: {
        count: 0,
        hasNextPage: false,
        hasPreviousPage: false,
        cursor: null,
      },
    };
  }
}