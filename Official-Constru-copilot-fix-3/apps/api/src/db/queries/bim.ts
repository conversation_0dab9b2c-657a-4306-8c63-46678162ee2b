import { eq, and, desc } from "drizzle-orm";
import type { Database } from "@api/db";
import { bimModels, modelAnnotations, users } from "@api/db/schema";

export async function getBimModels(
  db: Database,
  { teamId, pageSize = 50 }: { teamId: string; pageSize?: number },
) {
  return db
    .select({
      id: bimModels.id,
      documentId: bimModels.documentId,
      name: bimModels.name,
      fileFormat: bimModels.fileFormat,
      processingStatus: bimModels.processingStatus,
      metadata: bimModels.metadata,
      createdAt: bimModels.createdAt,
      updatedAt: bimModels.updatedAt,
    })
    .from(bimModels)
    .where(eq(bimModels.teamId, teamId))
    .orderBy(desc(bimModels.createdAt))
    .limit(pageSize);
}

export async function getBimModelById(
  db: Database,
  { id, teamId }: { id: string; teamId: string },
) {
  const result = await db
    .select({
      id: bimModels.id,
      documentId: bimModels.documentId,
      name: bimModels.name,
      fileFormat: bimModels.fileFormat,
      processingStatus: bimModels.processingStatus,
      metadata: bimModels.metadata,
      createdAt: bimModels.createdAt,
      updatedAt: bimModels.updatedAt,
    })
    .from(bimModels)
    .where(and(eq(bimModels.id, id), eq(bimModels.teamId, teamId)))
    .limit(1);

  return result[0] || null;
}

export async function getModelAnnotations(
  db: Database,
  { bimModelId, teamId }: { bimModelId: string; teamId: string },
) {
  return db
    .select({
      id: modelAnnotations.id,
      title: modelAnnotations.title,
      description: modelAnnotations.description,
      type: modelAnnotations.type,
      status: modelAnnotations.status,
      position: modelAnnotations.position,
      createdAt: modelAnnotations.createdAt,
      user: {
        id: users.id,
        name: users.fullName,
        email: users.email,
      },
    })
    .from(modelAnnotations)
    .innerJoin(users, eq(modelAnnotations.userId, users.id))
    .where(
      and(
        eq(modelAnnotations.bimModelId, bimModelId),
        eq(modelAnnotations.teamId, teamId),
      ),
    )
    .orderBy(desc(modelAnnotations.createdAt));
}

export async function createModelAnnotation(
  db: Database,
  data: {
    bimModelId: string;
    teamId: string;
    userId: string;
    title: string;
    description?: string;
    type?: string;
    position?: any;
  },
) {
  const result = await db
    .insert(modelAnnotations)
    .values({
      bimModelId: data.bimModelId,
      teamId: data.teamId,
      userId: data.userId,
      title: data.title,
      description: data.description,
      type: data.type || "comment",
      position: data.position,
    })
    .returning({
      id: modelAnnotations.id,
      title: modelAnnotations.title,
      description: modelAnnotations.description,
      type: modelAnnotations.type,
      status: modelAnnotations.status,
      position: modelAnnotations.position,
      createdAt: modelAnnotations.createdAt,
    });

  return result[0];
}