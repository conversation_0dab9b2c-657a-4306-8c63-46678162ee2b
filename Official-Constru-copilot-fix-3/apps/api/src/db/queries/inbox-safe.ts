import type { Database } from "@api/db";
import type { GetInboxParams } from "./inbox";
import { getInbox as getInboxUnsafe } from "./inbox";

/**
 * Safe wrapper for getInbox that handles errors gracefully
 */
export async function getInboxSafe(
  db: Database,
  params: GetInboxParams,
) {
  try {
    return await getInboxUnsafe(db, params);
  } catch (error) {
    console.error("Error fetching inbox:", error);
    
    // Return empty results with pagination info
    return {
      meta: {
        cursor: undefined,
        hasPreviousPage: false,
        hasNextPage: false,
      },
      data: [],
    };
  }
}