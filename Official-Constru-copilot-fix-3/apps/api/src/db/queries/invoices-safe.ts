import type { Database } from "@api/db";
import { sql } from "drizzle-orm";

/**
 * Safe version of getNextInvoiceNumber that handles missing database functions
 */
export async function getNextInvoiceNumberSafe(
  db: Database,
  teamId: string,
): Promise<string> {
  try {
    const [row] = await db.executeOnReplica(
      sql`SELECT get_next_invoice_number(${teamId}) AS next_invoice_number`,
    );

    if (!row) {
      throw new Error("Failed to fetch next invoice number");
    }

    return row.next_invoice_number as string;
  } catch (error) {
    console.error("Error getting next invoice number:", error);
    
    // Generate a fallback invoice number based on timestamp
    const timestamp = Date.now();
    const lastFourDigits = timestamp.toString().slice(-4);
    return `INV-${lastFourDigits}`;
  }
}