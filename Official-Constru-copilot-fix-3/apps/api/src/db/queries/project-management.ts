import type { Database } from "@api/db";
import { 
  projectPhases, 
  projectTasks, 
  projectResources, 
  projectTeamMembers,
  projectInspections,
  safetyIncidents,
  changeOrders,
  projectCommunications,
  projectProgressLogs,
  projectTemplates,
  trackerProjects,
  users,
  customers,
  teams
} from "@api/db/schema";
import { and, eq, desc, asc, sql, inArray, gte, lte } from "drizzle-orm";
import type { SQL } from "drizzle-orm/sql/sql";

// Project Phases
export type CreateProjectPhaseParams = {
  projectId: string;
  name: string;
  description?: string;
  phaseOrder: number;
  estimatedStart?: string;
  estimatedEnd?: string;
  dependencies?: string[];
};

export async function createProjectPhase(
  db: Database,
  params: CreateProjectPhaseParams
) {
  const [result] = await db
    .insert(projectPhases)
    .values({
      projectId: params.projectId,
      name: params.name,
      description: params.description,
      phaseOrder: params.phaseOrder,
      estimatedStart: params.estimatedStart,
      estimatedEnd: params.estimatedEnd,
      dependencies: params.dependencies || []
    })
    .returning();

  return result;
}

export async function getProjectPhases(
  db: Database,
  projectId: string
) {
  return db
    .select()
    .from(projectPhases)
    .where(eq(projectPhases.projectId, projectId))
    .orderBy(asc(projectPhases.phaseOrder));
}

// Project Tasks
export type CreateProjectTaskParams = {
  projectId: string;
  phaseId?: string;
  parentTaskId?: string;
  name: string;
  description?: string;
  taskOrder?: number;
  priority?: 'low' | 'medium' | 'high' | 'critical';
  estimatedHours?: string;
  estimatedCost?: string;
  estimatedStart?: string;
  estimatedEnd?: string;
  assignedTo?: string;
  dependencies?: string[];
};

export async function createProjectTask(
  db: Database,
  params: CreateProjectTaskParams,
  createdBy: string
) {
  const [result] = await db
    .insert(projectTasks)
    .values({
      projectId: params.projectId,
      phaseId: params.phaseId,
      parentTaskId: params.parentTaskId,
      name: params.name,
      description: params.description,
      taskOrder: params.taskOrder,
      priority: params.priority,
      estimatedHours: params.estimatedHours ? Number(params.estimatedHours) : undefined,
      estimatedCost: params.estimatedCost ? Number(params.estimatedCost) : undefined,
      estimatedStart: params.estimatedStart,
      estimatedEnd: params.estimatedEnd,
      assignedTo: params.assignedTo,
      dependencies: params.dependencies || [],
      createdBy
    })
    .returning();

  return result;
}

export async function getProjectTasks(
  db: Database,
  params: {
    projectId: string;
    phaseId?: string;
    assignedTo?: string;
    status?: 'not_started' | 'in_progress' | 'on_hold' | 'completed' | 'cancelled' | 'blocked';
  }
) {
  const whereConditions: SQL[] = [eq(projectTasks.projectId, params.projectId)];
  
  if (params.phaseId) {
    whereConditions.push(eq(projectTasks.phaseId, params.phaseId));
  }
  
  if (params.assignedTo) {
    whereConditions.push(eq(projectTasks.assignedTo, params.assignedTo));
  }
  
  if (params.status) {
    whereConditions.push(eq(projectTasks.status, params.status));
  }

  return db
    .select({
      id: projectTasks.id,
      projectId: projectTasks.projectId,
      phaseId: projectTasks.phaseId,
      parentTaskId: projectTasks.parentTaskId,
      name: projectTasks.name,
      description: projectTasks.description,
      taskOrder: projectTasks.taskOrder,
      status: projectTasks.status,
      priority: projectTasks.priority,
      estimatedHours: projectTasks.estimatedHours,
      actualHours: projectTasks.actualHours,
      estimatedCost: projectTasks.estimatedCost,
      actualCost: projectTasks.actualCost,
      startDate: projectTasks.startDate,
      endDate: projectTasks.endDate,
      estimatedStart: projectTasks.estimatedStart,
      estimatedEnd: projectTasks.estimatedEnd,
      completionPercentage: projectTasks.completionPercentage,
      dependencies: projectTasks.dependencies,
      createdAt: projectTasks.createdAt,
      updatedAt: projectTasks.updatedAt,
      assignedUser: {
        id: users.id,
        fullName: users.fullName,
        email: users.email,
        avatarUrl: users.avatarUrl
      }
    })
    .from(projectTasks)
    .leftJoin(users, eq(projectTasks.assignedTo, users.id))
    .where(and(...whereConditions))
    .orderBy(asc(projectTasks.taskOrder));
}

export async function updateTaskProgress(
  db: Database,
  taskId: string,
  params: {
    status?: 'not_started' | 'in_progress' | 'on_hold' | 'completed' | 'cancelled' | 'blocked';
    completionPercentage?: string;
    actualHours?: string;
    actualCost?: string;
    startDate?: string;
    endDate?: string;
  }
) {
  const updateData: any = {
    updatedAt: sql`NOW()`
  };
  
  if (params.status) updateData.status = params.status;
  if (params.completionPercentage) updateData.completionPercentage = Number(params.completionPercentage);
  if (params.actualHours) updateData.actualHours = Number(params.actualHours);
  if (params.actualCost) updateData.actualCost = Number(params.actualCost);
  if (params.startDate) updateData.startDate = params.startDate;
  if (params.endDate) updateData.endDate = params.endDate;
  
  const [result] = await db
    .update(projectTasks)
    .set(updateData)
    .where(eq(projectTasks.id, taskId))
    .returning();

  return result;
}

// Project Team Management
export type AssignTeamMemberParams = {
  projectId: string;
  userId: string;
  hourlyRate?: string;
  startDate?: string;
  endDate?: string;
  permissions?: any;
};

export async function assignTeamMember(
  db: Database,
  params: AssignTeamMemberParams
) {
  const insertData = {
    projectId: params.projectId,
    userId: params.userId,
    hourlyRate: params.hourlyRate ? Number(params.hourlyRate) : undefined,
    startDate: params.startDate,
    endDate: params.endDate,
    permissions: params.permissions
  };
  
  const [result] = await db
    .insert(projectTeamMembers)
    .values(insertData)
    .onConflictDoUpdate({
      target: [projectTeamMembers.projectId, projectTeamMembers.userId],
      set: {
        hourlyRate: params.hourlyRate ? Number(params.hourlyRate) : undefined,
        startDate: params.startDate,
        endDate: params.endDate,
        permissions: params.permissions,
        isActive: true
      }
    })
    .returning();

  return result;
}

export async function getProjectTeam(
  db: Database,
  projectId: string
) {
  return db
    .select({
      id: projectTeamMembers.id,
      projectId: projectTeamMembers.projectId,
      userId: projectTeamMembers.userId,
      hourlyRate: projectTeamMembers.hourlyRate,
      startDate: projectTeamMembers.startDate,
      endDate: projectTeamMembers.endDate,
      isActive: projectTeamMembers.isActive,
      permissions: projectTeamMembers.permissions,
      createdAt: projectTeamMembers.createdAt,
      user: {
        id: users.id,
        fullName: users.fullName,
        email: users.email,
        avatarUrl: users.avatarUrl
      }
    })
    .from(projectTeamMembers)
    .innerJoin(users, eq(projectTeamMembers.userId, users.id))
    .where(and(
      eq(projectTeamMembers.projectId, projectId),
      eq(projectTeamMembers.isActive, true)
    ))
    .orderBy(asc(projectTeamMembers.createdAt));
}

// Project Resources
export type CreateResourceParams = {
  projectId: string;
  taskId?: string;
  resourceType: 'labor' | 'equipment' | 'material' | 'subcontractor' | 'permit';
  name: string;
  description?: string;
  quantity: string;
  unit?: string;
  unitCost?: string;
  totalCost?: string;
  vendorContact?: string;
  availabilityStart?: string;
  availabilityEnd?: string;
};

export async function createProjectResource(
  db: Database,
  params: CreateResourceParams
) {
  const [result] = await db
    .insert(projectResources)
    .values({
      projectId: params.projectId,
      taskId: params.taskId,
      resourceType: params.resourceType,
      name: params.name,
      description: params.description,
      quantity: Number(params.quantity),
      unit: params.unit,
      unitCost: params.unitCost ? Number(params.unitCost) : undefined,
      totalCost: params.totalCost ? Number(params.totalCost) : undefined,
      vendorContact: params.vendorContact,
      availabilityStart: params.availabilityStart,
      availabilityEnd: params.availabilityEnd
    })
    .returning();

  return result;
}

export async function getProjectResources(
  db: Database,
  projectId: string,
  resourceType?: 'labor' | 'equipment' | 'material' | 'subcontractor' | 'permit'
) {
  const whereConditions: SQL[] = [eq(projectResources.projectId, projectId)];
  
  if (resourceType) {
    whereConditions.push(eq(projectResources.resourceType, resourceType));
  }

  return db
    .select()
    .from(projectResources)
    .where(and(...whereConditions))
    .orderBy(asc(projectResources.resourceType), asc(projectResources.name));
}

// Enhanced Project Dashboard Data
export async function getProjectDashboardData(
  db: Database,
  params: {
    teamId: string;
    userId?: string;
    projectIds?: string[];
    status?: 'in_progress' | 'completed';
    limit?: number;
  }
) {
  const { teamId, userId, projectIds, status, limit = 50 } = params;
  
  const whereConditions: SQL[] = [eq(trackerProjects.teamId, teamId)];
  
  if (status) {
    whereConditions.push(eq(trackerProjects.status, status));
  }
  
  if (projectIds && projectIds.length > 0) {
    whereConditions.push(inArray(trackerProjects.id, projectIds));
  }

  // If userId is provided, filter by projects where user is a team member
  if (userId) {
    whereConditions.push(
      sql`${trackerProjects.id} IN (
        SELECT ${projectTeamMembers.projectId}
        FROM ${projectTeamMembers}
        WHERE ${projectTeamMembers.userId} = ${userId}
        AND ${projectTeamMembers.isActive} = true
      )`
    );
  }

  const projects = await db
    .select({
      id: trackerProjects.id,
      name: trackerProjects.name,
      description: trackerProjects.description,
      status: trackerProjects.status,
      projectType: trackerProjects.projectType,
      priority: trackerProjects.priority,
      completionPercentage: trackerProjects.completionPercentage,
      budget: trackerProjects.budget,
      actualCost: trackerProjects.actualCost,
      plannedStartDate: trackerProjects.plannedStartDate,
      plannedEndDate: trackerProjects.plannedEndDate,
      actualStartDate: trackerProjects.actualStartDate,
      actualEndDate: trackerProjects.actualEndDate,
      locationAddress: trackerProjects.locationAddress,
      createdAt: trackerProjects.createdAt,
      customer: {
        id: customers.id,
        name: customers.name,
        email: customers.email
      },
      projectManager: {
        id: users.id,
        fullName: users.fullName,
        email: users.email,
        avatarUrl: users.avatarUrl
      }
    })
    .from(trackerProjects)
    .leftJoin(customers, eq(trackerProjects.customerId, customers.id))
    .leftJoin(users, eq(trackerProjects.projectManagerId, users.id))
    .where(and(...whereConditions))
    .orderBy(desc(trackerProjects.createdAt))
    .limit(limit);

  // Get additional data for each project
  const projectIds_result = projects.map(p => p.id);
  
  // Get task counts
  const taskCounts = projectIds_result.length > 0 ? await db
    .select({
      projectId: projectTasks.projectId,
      totalTasks: sql<number>`COUNT(*)`,
      completedTasks: sql<number>`COUNT(*) FILTER (WHERE ${projectTasks.status} = 'completed')`,
      inProgressTasks: sql<number>`COUNT(*) FILTER (WHERE ${projectTasks.status} = 'in_progress')`,
      overdueTasks: sql<number>`COUNT(*) FILTER (WHERE ${projectTasks.status} != 'completed' AND ${projectTasks.estimatedEnd} < CURRENT_DATE)`
    })
    .from(projectTasks)
    .where(inArray(projectTasks.projectId, projectIds_result))
    .groupBy(projectTasks.projectId) : [];

  // Get team member counts
  const teamCounts = projectIds_result.length > 0 ? await db
    .select({
      projectId: projectTeamMembers.projectId,
      teamSize: sql<number>`COUNT(DISTINCT ${projectTeamMembers.userId})`
    })
    .from(projectTeamMembers)
    .where(and(
      inArray(projectTeamMembers.projectId, projectIds_result),
      eq(projectTeamMembers.isActive, true)
    ))
    .groupBy(projectTeamMembers.projectId) : [];

  // Get recent activities
  const recentActivities = projectIds_result.length > 0 ? await db
    .select({
      projectId: projectProgressLogs.projectId,
      recentActivity: sql<string>`string_agg(${projectProgressLogs.workDescription}, '; ' ORDER BY ${projectProgressLogs.progressDate} DESC)`
    })
    .from(projectProgressLogs)
    .where(and(
      inArray(projectProgressLogs.projectId, projectIds_result),
      gte(projectProgressLogs.progressDate, sql`CURRENT_DATE - INTERVAL '7 days'`)
    ))
    .groupBy(projectProgressLogs.projectId) : [];

  // Combine all data
  return projects.map(project => {
    const taskData = taskCounts.find(tc => tc.projectId === project.id);
    const teamData = teamCounts.find(tc => tc.projectId === project.id);
    const activityData = recentActivities.find(ra => ra.projectId === project.id);

    return {
      ...project,
      totalTasks: taskData?.totalTasks || 0,
      completedTasks: taskData?.completedTasks || 0,
      inProgressTasks: taskData?.inProgressTasks || 0,
      overdueTasks: taskData?.overdueTasks || 0,
      teamSize: teamData?.teamSize || 0,
      recentActivity: activityData?.recentActivity || null
    };
  });
}

// Project Communications
export type CreateCommunicationParams = {
  projectId: string;
  senderId: string;
  recipients: string[];
  communicationType?: string;
  subject?: string;
  content: string;
  priority?: 'low' | 'medium' | 'high' | 'critical';
  attachments?: string[];
  threadId?: string;
};

export async function createProjectCommunication(
  db: Database,
  params: CreateCommunicationParams
) {
  const [result] = await db
    .insert(projectCommunications)
    .values({
      ...params,
      communicationType: params.communicationType || 'message',
      priority: params.priority || 'medium'
    })
    .returning();

  return result;
}

export async function getProjectCommunications(
  db: Database,
  projectId: string,
  userId?: string
) {
  const whereConditions: SQL[] = [eq(projectCommunications.projectId, projectId)];
  
  if (userId) {
    whereConditions.push(
      sql`(${projectCommunications.senderId} = ${userId} OR ${userId} = ANY(${projectCommunications.recipients}))`
    );
  }

  return db
    .select({
      id: projectCommunications.id,
      projectId: projectCommunications.projectId,
      communicationType: projectCommunications.communicationType,
      subject: projectCommunications.subject,
      content: projectCommunications.content,
      priority: projectCommunications.priority,
      recipients: projectCommunications.recipients,
      readBy: projectCommunications.readBy,
      attachments: projectCommunications.attachments,
      threadId: projectCommunications.threadId,
      createdAt: projectCommunications.createdAt,
      sender: {
        id: users.id,
        fullName: users.fullName,
        email: users.email,
        avatarUrl: users.avatarUrl
      }
    })
    .from(projectCommunications)
    .innerJoin(users, eq(projectCommunications.senderId, users.id))
    .where(and(...whereConditions))
    .orderBy(desc(projectCommunications.createdAt));
}

// Role-based permission check
export async function checkProjectPermission(
  db: Database,
  userId: string,
  projectId: string,
  requiredRole?: string
) {
  const membership = await db
    .select({
      permissions: projectTeamMembers.permissions,
      isActive: projectTeamMembers.isActive
    })
    .from(projectTeamMembers)
    .where(and(
      eq(projectTeamMembers.userId, userId),
      eq(projectTeamMembers.projectId, projectId),
      eq(projectTeamMembers.isActive, true)
    ))
    .limit(1);

  if (!membership.length) {
    return { hasAccess: false, role: null };
  }

  const member = membership[0];
  
  if (requiredRole && member) {
    // Role is stored in permissions JSON field
    const memberRole = (member.permissions as any)?.role;
    
    const roleHierarchy = {
      'admin': 6,
      'project_manager': 5,
      'supervisor': 4,
      'foreman': 3,
      'subcontractor': 2,
      'worker': 1,
      'client': 0
    };
    
    const userRoleLevel = roleHierarchy[memberRole as keyof typeof roleHierarchy] || 0;
    const requiredRoleLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;
    
    return {
      hasAccess: userRoleLevel >= requiredRoleLevel,
      role: memberRole,
      permissions: member.permissions
    };
  }

  return {
    hasAccess: !!member,
    role: (member?.permissions as any)?.role || null,
    permissions: member?.permissions || null
  };
}