#!/usr/bin/env bun

import { connectDb } from "./src/db/index";
import { sql } from "drizzle-orm";

async function checkTrackerFunctions() {
  try {
    const db = await connectDb();
    
    // Check for tracker-related functions
    console.log("Checking for tracker-related functions...");
    const result = await db.execute(sql`
      SELECT routine_name 
      FROM information_schema.routines 
      WHERE routine_schema = 'public' 
      AND routine_name LIKE '%tracker%' OR routine_name LIKE '%tracked%'
      ORDER BY routine_name
    `);
    
    console.log("Tracker functions found:", result);
    
    // Check if the specific function exists
    const trackedUsersResult = await db.execute(sql`
      SELECT routine_name 
      FROM information_schema.routines 
      WHERE routine_schema = 'public' 
      AND routine_name = 'get_tracked_users_by_project_ids'
    `);
    
    if (trackedUsersResult.length === 0) {
      console.log("\n❌ Function 'get_tracked_users_by_project_ids' NOT FOUND");
      console.log("\nCreating missing tracker functions...");
      
      // Read and execute the migration
      const migrationContent = await Bun.file("./migrations/0002_fix_tracker_functions.sql").text();
      await db.execute(sql.raw(migrationContent));
      
      console.log("✅ Tracker functions created successfully!");
    } else {
      console.log("\n✅ Function 'get_tracked_users_by_project_ids' already exists");
    }
    
  } catch (error) {
    console.error("Error:", error);
  }
}

checkTrackerFunctions();