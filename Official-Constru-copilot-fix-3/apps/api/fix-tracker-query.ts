#!/usr/bin/env bun

import { connectDb } from "./src/db/index";
import { sql } from "drizzle-orm";

async function fixTrackerQuery() {
  try {
    const db = await connectDb();
    
    console.log("Creating tracker query function...");
    
    // Create the specific query that's failing
    await db.execute(sql`
      CREATE OR REPLACE FUNCTION get_tracked_users_by_project_ids(project_ids uuid[])
      RETURNS TABLE (project_id uuid, users jsonb)
      LANGUAGE plpgsql
      AS $$
      BEGIN
        RETURN QUERY
        SELECT 
          te.project_id,
          jsonb_agg(
            DISTINCT jsonb_build_object(
              'user_id', u.id,
              'full_name', COALESCE(u.full_name, u.email, 'Unknown'),
              'avatar_url', u.avatar_url
            ) ORDER BY (jsonb_build_object(
              'user_id', u.id,
              'full_name', COALESCE(u.full_name, u.email, 'Unknown'),
              'avatar_url', u.avatar_url
            ))->>'full_name'
          ) as users
        FROM tracker_entries te
        JOIN users u ON u.id = te.assigned_id
        WHERE te.project_id = ANY(project_ids)
          AND te.assigned_id IS NOT NULL
        GROUP BY te.project_id;
      END;
      $$;
    `);
    
    console.log("✅ Function created successfully!");
    
    // Test the function
    console.log("\nTesting the function...");
    const testResult = await db.execute(sql`
      SELECT * FROM get_tracked_users_by_project_ids(ARRAY[]::uuid[])
    `);
    console.log("Test result:", testResult);
    
  } catch (error) {
    console.error("Error:", error);
  }
}

fixTrackerQuery();