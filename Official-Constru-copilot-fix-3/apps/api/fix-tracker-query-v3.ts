#!/usr/bin/env bun

import { connectDb } from "./src/db/index";
import { sql } from "drizzle-orm";

async function fixTrackerQuery() {
  try {
    const db = await connectDb();
    
    console.log("Creating tracker query function (final version)...");
    
    // Drop the old function if it exists
    await db.execute(sql`
      DROP FUNCTION IF EXISTS get_tracked_users_by_project_ids(uuid[]);
    `);
    
    // Create the function with properly qualified column names
    await db.execute(sql`
      CREATE OR REPLACE FUNCTION get_tracked_users_by_project_ids(project_ids uuid[])
      RETURNS TABLE (project_id uuid, users jsonb)
      LANGUAGE plpgsql
      AS $$
      BEGIN
        RETURN QUERY
        WITH user_data AS (
          SELECT DISTINCT
            te.project_id as pid,
            u.id as user_id,
            COALESCE(u.full_name, u.email, 'Unknown') as full_name,
            u.avatar_url
          FROM tracker_entries te
          JOIN users u ON u.id = te.assigned_id
          WHERE te.project_id = ANY(project_ids)
            AND te.assigned_id IS NOT NULL
        )
        SELECT 
          ud.pid as project_id,
          jsonb_agg(
            jsonb_build_object(
              'user_id', ud.user_id,
              'full_name', ud.full_name,
              'avatar_url', ud.avatar_url
            ) ORDER BY ud.full_name
          ) as users
        FROM user_data ud
        GROUP BY ud.pid;
      END;
      $$;
    `);
    
    console.log("✅ Function created successfully!");
    
    // Test the function
    console.log("\nTesting the function with empty array...");
    const testResult = await db.execute(sql`
      SELECT * FROM get_tracked_users_by_project_ids(ARRAY[]::uuid[])
    `);
    console.log("Test result (should be empty):", testResult.length === 0 ? "Empty as expected" : testResult);
    
    // Test with a specific UUID
    console.log("\nTesting with a sample UUID...");
    const testResult2 = await db.execute(sql`
      SELECT * FROM get_tracked_users_by_project_ids(ARRAY['90cdd019-bc1f-4a35-a4a7-5e93be7cc487'::uuid])
    `);
    console.log("Test result 2:", testResult2.length === 0 ? "No data for this project" : testResult2);
    
    console.log("\n✅ All tests completed successfully!");
    
  } catch (error) {
    console.error("Error:", error);
  }
}

fixTrackerQuery();