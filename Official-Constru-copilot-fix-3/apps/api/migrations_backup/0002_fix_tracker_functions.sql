-- Ensure schema exists
CREATE SCHEMA IF NOT EXISTS private;

-- Drop and recreate the function to ensure it's correct
DROP FUNCTION IF EXISTS private.get_teams_for_authenticated_user();

CREATE OR REPLACE FUNCTION private.get_teams_for_authenticated_user()
RETURNS SETOF uuid
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
  SELECT team_id 
  FROM public.users_on_team 
  WHERE user_id = auth.uid()
  UNION
  SELECT team_id 
  FROM public.users 
  WHERE id = auth.uid() AND team_id IS NOT NULL;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION private.get_teams_for_authenticated_user() TO authenticated;

-- Ensure all RLS policies are enabled
ALTER TABLE tracker_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE tracker_projects ENABLE ROW LEVEL SECURITY;

-- Drop and recreate RLS policies for tracker_entries to ensure they work correctly
DROP POLICY IF EXISTS "Entries can be selected by a member of the team" ON tracker_entries;
DROP POLICY IF EXISTS "Entries can be created by a member of the team" ON tracker_entries;
DROP POLICY IF EXISTS "Entries can be updated by a member of the team" ON tracker_entries;
DROP POLICY IF EXISTS "Entries can be deleted by a member of the team" ON tracker_entries;

-- Create RLS policies for tracker_entries
CREATE POLICY "Entries can be selected by a member of the team" 
ON tracker_entries 
FOR SELECT 
TO authenticated 
USING (team_id IN (SELECT private.get_teams_for_authenticated_user()));

CREATE POLICY "Entries can be created by a member of the team" 
ON tracker_entries 
FOR INSERT 
TO authenticated 
WITH CHECK (team_id IN (SELECT private.get_teams_for_authenticated_user()));

CREATE POLICY "Entries can be updated by a member of the team" 
ON tracker_entries 
FOR UPDATE 
TO authenticated 
USING (team_id IN (SELECT private.get_teams_for_authenticated_user()));

CREATE POLICY "Entries can be deleted by a member of the team" 
ON tracker_entries 
FOR DELETE 
TO authenticated 
USING (team_id IN (SELECT private.get_teams_for_authenticated_user()));

-- Drop and recreate RLS policies for tracker_projects to ensure they work correctly
DROP POLICY IF EXISTS "Projects can be selected by a member of the team" ON tracker_projects;
DROP POLICY IF EXISTS "Projects can be created by a member of the team" ON tracker_projects;
DROP POLICY IF EXISTS "Projects can be updated by a member of the team" ON tracker_projects;
DROP POLICY IF EXISTS "Projects can be deleted by a member of the team" ON tracker_projects;

-- Create RLS policies for tracker_projects
CREATE POLICY "Projects can be selected by a member of the team" 
ON tracker_projects 
FOR SELECT 
TO authenticated 
USING (team_id IN (SELECT private.get_teams_for_authenticated_user()));

CREATE POLICY "Projects can be created by a member of the team" 
ON tracker_projects 
FOR INSERT 
TO authenticated 
WITH CHECK (team_id IN (SELECT private.get_teams_for_authenticated_user()));

CREATE POLICY "Projects can be updated by a member of the team" 
ON tracker_projects 
FOR UPDATE 
TO authenticated 
USING (team_id IN (SELECT private.get_teams_for_authenticated_user()));

CREATE POLICY "Projects can be deleted by a member of the team" 
ON tracker_projects 
FOR DELETE 
TO authenticated 
USING (team_id IN (SELECT private.get_teams_for_authenticated_user()));

-- Ensure indexes exist
CREATE INDEX IF NOT EXISTS tracker_entries_team_id_idx ON tracker_entries(team_id);
CREATE INDEX IF NOT EXISTS tracker_entries_project_id_idx ON tracker_entries(project_id);
CREATE INDEX IF NOT EXISTS tracker_entries_assigned_id_idx ON tracker_entries(assigned_id);
CREATE INDEX IF NOT EXISTS tracker_entries_date_idx ON tracker_entries(date);

-- Grant necessary permissions
GRANT ALL ON tracker_entries TO authenticated;
GRANT ALL ON tracker_projects TO authenticated;
GRANT USAGE ON SCHEMA private TO authenticated;