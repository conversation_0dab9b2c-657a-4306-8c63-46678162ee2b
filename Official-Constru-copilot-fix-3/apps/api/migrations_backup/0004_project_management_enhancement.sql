-- Comprehensive Project Management Enhancement for Constru
-- This enhances the existing tracker_projects into a full construction management system

-- 1. Enhanced Project Types and Templates
CREATE TYPE IF NOT EXISTS project_type AS ENUM (
  'residential_construction',
  'commercial_construction', 
  'renovation',
  'infrastructure',
  'maintenance',
  'custom'
);

CREATE TYPE IF NOT EXISTS project_priority AS ENUM ('low', 'medium', 'high', 'critical');

CREATE TYPE IF NOT EXISTS task_status AS ENUM (
  'not_started',
  'in_progress', 
  'on_hold',
  'completed',
  'cancelled',
  'blocked'
);

CREATE TYPE IF NOT EXISTS resource_type AS ENUM (
  'labor',
  'equipment', 
  'material',
  'subcontractor',
  'permit'
);

CREATE TYPE IF NOT EXISTS user_role AS ENUM (
  'project_manager',
  'foreman',
  'worker',
  'client',
  'supervisor',
  'admin',
  'subcontractor'
);

-- 2. Project Templates Table
CREATE TABLE IF NOT EXISTS project_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  project_type project_type NOT NULL,
  team_id UUID NOT NULL REFERENCES teams(id) ON DELETE CASCADE,
  estimated_duration_days INTEGER,
  template_data JSONB, -- Contains default phases, tasks, resources
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES users(id) ON DELETE SET NULL
);

-- 3. Enhanced Project Status and Phases
CREATE TABLE IF NOT EXISTS project_phases (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES tracker_projects(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  phase_order INTEGER NOT NULL,
  start_date DATE,
  end_date DATE,
  estimated_start DATE,
  estimated_end DATE,
  status task_status DEFAULT 'not_started',
  completion_percentage DECIMAL(5,2) DEFAULT 0,
  dependencies UUID[], -- Array of phase IDs this depends on
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 4. Detailed Task Management
CREATE TABLE IF NOT EXISTS project_tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES tracker_projects(id) ON DELETE CASCADE,
  phase_id UUID REFERENCES project_phases(id) ON DELETE SET NULL,
  parent_task_id UUID REFERENCES project_tasks(id) ON DELETE SET NULL,
  name TEXT NOT NULL,
  description TEXT,
  task_order INTEGER,
  status task_status DEFAULT 'not_started',
  priority project_priority DEFAULT 'medium',
  estimated_hours DECIMAL(8,2),
  actual_hours DECIMAL(8,2) DEFAULT 0,
  estimated_cost DECIMAL(12,2),
  actual_cost DECIMAL(12,2) DEFAULT 0,
  start_date DATE,
  end_date DATE,
  estimated_start DATE,
  estimated_end DATE,
  completion_percentage DECIMAL(5,2) DEFAULT 0,
  dependencies UUID[], -- Array of task IDs this depends on
  assigned_to UUID REFERENCES users(id) ON DELETE SET NULL,
  created_by UUID REFERENCES users(id) ON DELETE SET NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Full-text search
  fts TSVECTOR GENERATED ALWAYS AS (
    to_tsvector('english', COALESCE(name, '') || ' ' || COALESCE(description, ''))
  ) STORED
);

-- 5. Resource Management
CREATE TABLE IF NOT EXISTS project_resources (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES tracker_projects(id) ON DELETE CASCADE,
  task_id UUID REFERENCES project_tasks(id) ON DELETE SET NULL,
  resource_type resource_type NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  quantity DECIMAL(10,2) NOT NULL DEFAULT 1,
  unit TEXT, -- 'hours', 'pieces', 'tons', etc.
  unit_cost DECIMAL(10,2),
  total_cost DECIMAL(12,2),
  vendor_contact TEXT,
  availability_start DATE,
  availability_end DATE,
  allocated BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 6. Enhanced Team Assignment with Roles
CREATE TABLE IF NOT EXISTS project_team_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES tracker_projects(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  role user_role NOT NULL,
  hourly_rate DECIMAL(8,2),
  start_date DATE,
  end_date DATE,
  is_active BOOLEAN DEFAULT TRUE,
  permissions JSONB, -- Role-specific permissions
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(project_id, user_id, role)
);

-- 7. Quality Control and Inspections
CREATE TABLE IF NOT EXISTS project_inspections (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES tracker_projects(id) ON DELETE CASCADE,
  task_id UUID REFERENCES project_tasks(id) ON DELETE SET NULL,
  phase_id UUID REFERENCES project_phases(id) ON DELETE SET NULL,
  inspector_id UUID REFERENCES users(id) ON DELETE SET NULL,
  inspection_type TEXT NOT NULL,
  scheduled_date DATE,
  completed_date DATE,
  status TEXT DEFAULT 'scheduled',
  passed BOOLEAN,
  notes TEXT,
  checklist_items JSONB, -- Array of checklist items with pass/fail
  attachments UUID[], -- Document IDs
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 8. Safety Management
CREATE TABLE IF NOT EXISTS safety_incidents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES tracker_projects(id) ON DELETE CASCADE,
  reported_by UUID REFERENCES users(id) ON DELETE SET NULL,
  incident_date DATE NOT NULL,
  incident_type TEXT NOT NULL,
  severity TEXT NOT NULL,
  description TEXT NOT NULL,
  location TEXT,
  injured_person TEXT,
  witnesses TEXT[],
  immediate_action TEXT,
  root_cause TEXT,
  preventive_measures TEXT,
  status TEXT DEFAULT 'open',
  attachments UUID[], -- Document IDs
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 9. Change Orders
CREATE TABLE IF NOT EXISTS change_orders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES tracker_projects(id) ON DELETE CASCADE,
  change_order_number TEXT NOT NULL,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  reason TEXT,
  requested_by UUID REFERENCES users(id) ON DELETE SET NULL,
  approved_by UUID REFERENCES users(id) ON DELETE SET NULL,
  cost_impact DECIMAL(12,2) DEFAULT 0,
  time_impact_days INTEGER DEFAULT 0,
  status TEXT DEFAULT 'pending',
  priority project_priority DEFAULT 'medium',
  request_date DATE DEFAULT CURRENT_DATE,
  approval_date DATE,
  implementation_date DATE,
  attachments UUID[], -- Document IDs
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 10. Project Communications
CREATE TABLE IF NOT EXISTS project_communications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES tracker_projects(id) ON DELETE CASCADE,
  sender_id UUID REFERENCES users(id) ON DELETE SET NULL,
  recipients UUID[] NOT NULL, -- Array of user IDs
  communication_type TEXT DEFAULT 'message', -- 'message', 'update', 'alert', 'announcement'
  subject TEXT,
  content TEXT NOT NULL,
  priority project_priority DEFAULT 'medium',
  read_by JSONB DEFAULT '{}', -- Track who has read the message
  attachments UUID[], -- Document IDs
  thread_id UUID, -- For threaded conversations
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Full-text search
  fts TSVECTOR GENERATED ALWAYS AS (
    to_tsvector('english', COALESCE(subject, '') || ' ' || COALESCE(content, ''))
  ) STORED
);

-- 11. Project Progress Tracking
CREATE TABLE IF NOT EXISTS project_progress_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES tracker_projects(id) ON DELETE CASCADE,
  task_id UUID REFERENCES project_tasks(id) ON DELETE SET NULL,
  phase_id UUID REFERENCES project_phases(id) ON DELETE SET NULL,
  logged_by UUID REFERENCES users(id) ON DELETE SET NULL,
  progress_date DATE DEFAULT CURRENT_DATE,
  progress_percentage DECIMAL(5,2),
  hours_worked DECIMAL(8,2),
  work_description TEXT,
  issues_encountered TEXT,
  photos UUID[], -- Document IDs for progress photos
  weather_conditions TEXT,
  location TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 12. Enhanced Project Table (extend existing tracker_projects)
-- Add new columns to existing tracker_projects table
ALTER TABLE tracker_projects 
ADD COLUMN IF NOT EXISTS project_type project_type DEFAULT 'custom',
ADD COLUMN IF NOT EXISTS priority project_priority DEFAULT 'medium',
ADD COLUMN IF NOT EXISTS planned_start_date DATE,
ADD COLUMN IF NOT EXISTS planned_end_date DATE,
ADD COLUMN IF NOT EXISTS actual_start_date DATE,
ADD COLUMN IF NOT EXISTS actual_end_date DATE,
ADD COLUMN IF NOT EXISTS budget DECIMAL(15,2),
ADD COLUMN IF NOT EXISTS actual_cost DECIMAL(15,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS completion_percentage DECIMAL(5,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS project_manager_id UUID REFERENCES users(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS client_contact_info JSONB,
ADD COLUMN IF NOT EXISTS location_address TEXT,
ADD COLUMN IF NOT EXISTS location_coordinates POINT,
ADD COLUMN IF NOT EXISTS weather_dependent BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS permit_required BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS permit_status TEXT DEFAULT 'not_required',
ADD COLUMN IF NOT EXISTS template_id UUID REFERENCES project_templates(id) ON DELETE SET NULL;

-- 13. Create Indexes for Performance
CREATE INDEX IF NOT EXISTS idx_project_phases_project_id ON project_phases(project_id);
CREATE INDEX IF NOT EXISTS idx_project_phases_status ON project_phases(status);
CREATE INDEX IF NOT EXISTS idx_project_tasks_project_id ON project_tasks(project_id);
CREATE INDEX IF NOT EXISTS idx_project_tasks_phase_id ON project_tasks(phase_id);
CREATE INDEX IF NOT EXISTS idx_project_tasks_assigned_to ON project_tasks(assigned_to);
CREATE INDEX IF NOT EXISTS idx_project_tasks_status ON project_tasks(status);
CREATE INDEX IF NOT EXISTS idx_project_tasks_fts ON project_tasks USING GIN(fts);
CREATE INDEX IF NOT EXISTS idx_project_resources_project_id ON project_resources(project_id);
CREATE INDEX IF NOT EXISTS idx_project_resources_task_id ON project_resources(task_id);
CREATE INDEX IF NOT EXISTS idx_project_team_members_project_id ON project_team_members(project_id);
CREATE INDEX IF NOT EXISTS idx_project_team_members_user_id ON project_team_members(user_id);
CREATE INDEX IF NOT EXISTS idx_project_inspections_project_id ON project_inspections(project_id);
CREATE INDEX IF NOT EXISTS idx_safety_incidents_project_id ON safety_incidents(project_id);
CREATE INDEX IF NOT EXISTS idx_change_orders_project_id ON change_orders(project_id);
CREATE INDEX IF NOT EXISTS idx_project_communications_project_id ON project_communications(project_id);
CREATE INDEX IF NOT EXISTS idx_project_communications_fts ON project_communications USING GIN(fts);
CREATE INDEX IF NOT EXISTS idx_project_progress_logs_project_id ON project_progress_logs(project_id);
CREATE INDEX IF NOT EXISTS idx_tracker_projects_project_manager ON tracker_projects(project_manager_id);
CREATE INDEX IF NOT EXISTS idx_tracker_projects_type_status ON tracker_projects(project_type, status);

-- 14. Create Functions for Project Analytics
CREATE OR REPLACE FUNCTION get_project_completion_percentage(project_uuid UUID)
RETURNS DECIMAL(5,2) AS $$
DECLARE
  total_tasks INTEGER;
  completed_tasks INTEGER;
  result DECIMAL(5,2);
BEGIN
  SELECT COUNT(*) INTO total_tasks
  FROM project_tasks
  WHERE project_id = project_uuid;
  
  IF total_tasks = 0 THEN
    RETURN 0;
  END IF;
  
  SELECT COUNT(*) INTO completed_tasks
  FROM project_tasks
  WHERE project_id = project_uuid AND status = 'completed';
  
  result := (completed_tasks::DECIMAL / total_tasks::DECIMAL) * 100;
  
  -- Update the project completion percentage
  UPDATE tracker_projects
  SET completion_percentage = result
  WHERE id = project_uuid;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 15. Create Function for Project Health Score
CREATE OR REPLACE FUNCTION calculate_project_health_score(project_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
  budget_score INTEGER := 100;
  timeline_score INTEGER := 100;
  task_score INTEGER := 100;
  budget_utilization DECIMAL(5,2);
  overdue_tasks INTEGER;
  total_score INTEGER;
BEGIN
  -- Calculate budget score
  SELECT 
    CASE 
      WHEN budget > 0 THEN (actual_cost / budget) * 100
      ELSE 0
    END INTO budget_utilization
  FROM tracker_projects
  WHERE id = project_uuid;
  
  IF budget_utilization > 110 THEN
    budget_score := 50;
  ELSIF budget_utilization > 100 THEN
    budget_score := 75;
  END IF;
  
  -- Calculate timeline score
  SELECT COUNT(*) INTO overdue_tasks
  FROM project_tasks
  WHERE project_id = project_uuid
    AND end_date < CURRENT_DATE
    AND status NOT IN ('completed', 'cancelled');
  
  IF overdue_tasks > 5 THEN
    timeline_score := 50;
  ELSIF overdue_tasks > 0 THEN
    timeline_score := 75;
  END IF;
  
  -- Calculate task completion score
  SELECT get_project_completion_percentage(project_uuid) INTO task_score;
  
  -- Calculate total health score
  total_score := (budget_score + timeline_score + task_score) / 3;
  
  RETURN total_score;
END;
$$ LANGUAGE plpgsql;

-- 16. Create Triggers for Auto-updating Timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_project_phases_updated_at
  BEFORE UPDATE ON project_phases
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_project_tasks_updated_at
  BEFORE UPDATE ON project_tasks
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_project_resources_updated_at
  BEFORE UPDATE ON project_resources
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_project_inspections_updated_at
  BEFORE UPDATE ON project_inspections
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_safety_incidents_updated_at
  BEFORE UPDATE ON safety_incidents
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_change_orders_updated_at
  BEFORE UPDATE ON change_orders
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- 17. Create Views for Common Queries
CREATE OR REPLACE VIEW project_overview AS
SELECT 
  p.id,
  p.name,
  p.project_type,
  p.status,
  p.priority,
  p.budget,
  p.actual_cost,
  p.completion_percentage,
  p.planned_start_date,
  p.planned_end_date,
  p.project_manager_id,
  pm.name AS project_manager_name,
  pm.email AS project_manager_email,
  COUNT(DISTINCT pt.id) AS total_tasks,
  COUNT(DISTINCT CASE WHEN pt.status = 'completed' THEN pt.id END) AS completed_tasks,
  COUNT(DISTINCT ptm.user_id) AS team_size,
  p.team_id,
  t.name AS team_name
FROM tracker_projects p
LEFT JOIN users pm ON p.project_manager_id = pm.id
LEFT JOIN project_tasks pt ON p.id = pt.project_id
LEFT JOIN project_team_members ptm ON p.id = ptm.project_id AND ptm.is_active = TRUE
LEFT JOIN teams t ON p.team_id = t.id
GROUP BY p.id, pm.id, t.id;

-- 18. RLS Policies for new tables
ALTER TABLE project_phases ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_resources ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_inspections ENABLE ROW LEVEL SECURITY;
ALTER TABLE safety_incidents ENABLE ROW LEVEL SECURITY;
ALTER TABLE change_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_communications ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_progress_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_templates ENABLE ROW LEVEL SECURITY;

-- Create similar RLS policies for each new table
CREATE POLICY "Team members can view project data" ON project_phases
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM tracker_projects p
    WHERE p.id = project_phases.project_id
    AND p.team_id IN (
      SELECT team_id FROM users_on_team WHERE user_id = auth.uid()
    )
  )
);

-- Repeat similar policies for other tables...

-- Migration complete