-- Create a function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user() 
R<PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  INSERT INTO public.users (id, email, full_name, avatar_url, created_at)
  VALUES (
    new.id,
    new.email,
    COALESCE(new.raw_user_meta_data->>'full_name', ''),
    new.raw_user_meta_data->>'avatar_url',
    now()
  );
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger that fires after user creation in auth.users
CREATE OR REPLACE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Also handle user updates (in case email or metadata changes)
CREATE OR REPLACE FUNCTION public.handle_user_update()
R<PERSON><PERSON>NS trigger AS $$
BEGIN
  UPDATE public.users
  SET 
    email = new.email,
    full_name = COALESCE(new.raw_user_meta_data->>'full_name', old.raw_user_meta_data->>'full_name', ''),
    avatar_url = new.raw_user_meta_data->>'avatar_url'
  WHERE id = new.id;
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger for user updates
CREATE OR REPLACE TRIGGER on_auth_user_updated
  AFTER UPDATE ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_user_update();