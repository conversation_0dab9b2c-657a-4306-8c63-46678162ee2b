#!/usr/bin/env node

import { config } from "dotenv";
import { resolve } from "path";

// Load environment variables
config({ path: resolve(".env.local") });

const dbUrl = process.env.DATABASE_SESSION_POOLER;
console.log("Database URL exists:", !!dbUrl);
console.log(
  "Database URL preview:",
  dbUrl ? dbUrl.substring(0, 30) + "..." : "undefined"
);

// Test connection with drizzle
import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";

async function testConnection() {
  try {
    const client = postgres(dbUrl);
    const db = drizzle(client);

    console.log("Testing database connection...");
    const result = await client`SELECT version()`;
    console.log("✅ Database connection successful!");
    console.log("PostgreSQL version:", result[0].version);

    await client.end();
  } catch (error) {
    console.error("❌ Database connection failed:", error.message);
  }
}

testConnection();
