#!/usr/bin/env bun

import { connectDb } from "./src/db/index";
import { sql } from "drizzle-orm";

async function createMissingFunctionsSafe() {
  try {
    const db = await connectDb();
    
    console.log("Creating missing database functions (safe mode)...");
    
    // 1. Check and create generate_inbox function safely
    console.log("1. Fixing generate_inbox function...");
    await db.execute(sql`
      DO $$
      BEGIN
        -- Try to create or replace, handling parameter name conflicts
        BEGIN
          DROP FUNCTION IF EXISTS generate_inbox(integer);
        EXCEPTION
          WHEN others THEN
            NULL; -- Ignore errors
        END;
        
        -- Create the function with correct parameter name
        CREATE OR REPLACE FUNCTION generate_inbox(input_length integer DEFAULT 10)
        RETURNS text
        LANGUAGE plpgsql
        AS $func$
        DECLARE
          chars text := 'abcdefghijklmnopqrstuvwxyz0123456789';
          result text := '';
          i integer;
        BEGIN
          FOR i IN 1..input_length LOOP
            result := result || substr(chars, floor(random() * length(chars) + 1)::integer, 1);
          END LOOP;
          RETURN result;
        END;
        $func$;
      END
      $$;
    `);
    
    // 2. Create inbox FTS function safely
    console.log("2. Creating generate_inbox_fts function...");
    await db.execute(sql`
      CREATE OR REPLACE FUNCTION generate_inbox_fts(display_name text, product_names text)
      RETURNS tsvector
      LANGUAGE plpgsql
      AS $$
      BEGIN
        RETURN to_tsvector('english', COALESCE(display_name, '') || ' ' || COALESCE(product_names, ''));
      END;
      $$;
    `);
    
    // 3. Create extract_product_names function
    console.log("3. Creating extract_product_names function...");
    await db.execute(sql`
      CREATE OR REPLACE FUNCTION extract_product_names(products_json text)
      RETURNS text
      LANGUAGE plpgsql
      AS $$
      DECLARE
        result text := '';
      BEGIN
        -- Simple extraction for now - in production you'd parse JSON properly
        IF products_json IS NOT NULL AND products_json != '' THEN
          result := regexp_replace(products_json, '[{}"\[\]]', ' ', 'g');
          result := regexp_replace(result, '[:,]', ' ', 'g');
        END IF;
        RETURN COALESCE(result, '');
      END;
      $$;
    `);
    
    // 4. Create nanoid function safely
    console.log("4. Creating nanoid function...");
    await db.execute(sql`
      DO $$
      BEGIN
        BEGIN
          DROP FUNCTION IF EXISTS nanoid(integer);
        EXCEPTION
          WHEN others THEN
            NULL;
        END;
        
        CREATE OR REPLACE FUNCTION nanoid(input_length integer DEFAULT 21)
        RETURNS text
        LANGUAGE plpgsql
        AS $func$
        DECLARE
          alphabet text := '_-0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
          result text := '';
          i integer;
        BEGIN
          FOR i IN 1..input_length LOOP
            result := result || substr(alphabet, floor(random() * length(alphabet) + 1)::integer, 1);
          END LOOP;
          RETURN result;
        END;
        $func$;
      END
      $$;
    `);
    
    // 5. Test all the functions
    console.log("5. Testing the functions...");
    
    const testInbox = await db.execute(sql`
      SELECT generate_inbox(10) as inbox_id
    `);
    console.log("Generate inbox test:", testInbox[0] ? "✅ Working" : "❌ Failed");
    
    const testNanoid = await db.execute(sql`
      SELECT nanoid(24) as nano_id
    `);
    console.log("Nanoid test:", testNanoid[0] ? "✅ Working" : "❌ Failed");
    
    const testFts = await db.execute(sql`
      SELECT generate_inbox_fts('test document', 'product1 product2') as fts_result
    `);
    console.log("FTS test:", testFts[0] ? "✅ Working" : "❌ Failed");
    
    console.log("✅ All missing functions created/fixed successfully!");
    
  } catch (error) {
    console.error("❌ Error creating functions:", error);
  }
}

createMissingFunctionsSafe();