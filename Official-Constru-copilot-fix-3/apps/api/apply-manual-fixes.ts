#!/usr/bin/env bun

import { connectDb } from "./src/db/index";
import { sql } from "drizzle-orm";

async function applyManualFixes() {
  try {
    const db = await connectDb();
    
    console.log("Applying manual database fixes...");
    
    // 1. Fix bank account currencies function
    console.log("1. Creating get_bank_account_currencies function...");
    await db.execute(sql`
      CREATE OR REPLACE FUNCTION get_bank_account_currencies(team_uuid uuid)
      RETURNS TABLE (currency text)
      LANGUAGE plpgsql
      AS $$
      BEGIN
        RETURN QUERY
        SELECT DISTINCT ba.currency
        FROM bank_accounts ba
        WHERE ba.team_id = team_uuid
          AND ba.enabled = true
          AND ba.currency IS NOT NULL
        ORDER BY ba.currency;
      END;
      $$;
    `);
    
    // 2. Fix bank account balances function
    console.log("2. Creating get_bank_account_balances function...");
    await db.execute(sql`
      CREATE OR REPLACE FUNCTION get_bank_account_balances(team_uuid uuid)
      RETURNS TABLE (
        account_id uuid,
        name text,
        currency text,
        balance numeric,
        base_balance numeric,
        base_currency text
      )
      LANGUAGE plpgsql
      AS $$
      BEGIN
        RETURN QUERY
        SELECT 
          ba.id as account_id,
          ba.name,
          ba.currency,
          ba.balance,
          ba.base_balance,
          ba.base_currency
        FROM bank_accounts ba
        WHERE ba.team_id = team_uuid
          AND ba.enabled = true
        ORDER BY ba.name;
      END;
      $$;
    `);
    
    // 3. Add performance indexes
    console.log("3. Adding performance indexes...");
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_bank_accounts_team_id_enabled ON bank_accounts(team_id, enabled);
      CREATE INDEX IF NOT EXISTS idx_bank_accounts_currency ON bank_accounts(currency);
      CREATE INDEX IF NOT EXISTS idx_tracker_entries_project_assigned ON tracker_entries(project_id, assigned_id);
      CREATE INDEX IF NOT EXISTS idx_tracker_projects_team_status ON tracker_projects(team_id, status);
    `);
    
    // 4. Test the functions
    console.log("4. Testing the functions...");
    
    // Test currencies function
    const currenciesTest = await db.execute(sql`
      SELECT * FROM get_bank_account_currencies('********-0000-0000-0000-************'::uuid)
    `);
    console.log("Currencies function test:", currenciesTest.length === 0 ? "✅ Working (no data)" : currenciesTest);
    
    // Test balances function  
    const balancesTest = await db.execute(sql`
      SELECT * FROM get_bank_account_balances('********-0000-0000-0000-************'::uuid)
    `);
    console.log("Balances function test:", balancesTest.length === 0 ? "✅ Working (no data)" : balancesTest);
    
    console.log("✅ All manual fixes applied successfully!");
    
  } catch (error) {
    console.error("❌ Error applying fixes:", error);
    process.exit(1);
  }
}

applyManualFixes();