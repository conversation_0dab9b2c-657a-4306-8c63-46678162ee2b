#!/usr/bin/env bun

import { connectDb } from "./src/db/index";
import { sql } from "drizzle-orm";

async function createMissingFunctions() {
  try {
    const db = await connectDb();
    
    console.log("Creating missing database functions...");
    
    // 1. Ensure private schema exists and create get_teams_for_authenticated_user function
    console.log("1. Creating private.get_teams_for_authenticated_user function...");
    await db.execute(sql`
      CREATE SCHEMA IF NOT EXISTS private;
      
      CREATE OR REPLACE FUNCTION private.get_teams_for_authenticated_user()
      RETURNS SETOF uuid
      LANGUAGE sql
      STABLE
      SECURITY DEFINER
      AS $$
        SELECT team_id 
        FROM public.users_on_team 
        WHERE user_id = auth.uid()
        UNION
        SELECT team_id 
        FROM public.users 
        WHERE id = auth.uid() AND team_id IS NOT NULL;
      $$;
      
      GRANT EXECUTE ON FUNCTION private.get_teams_for_authenticated_user() TO authenticated;
    `);
    
    // 2. Create global search function
    console.log("2. Creating global_search function...");
    await db.execute(sql`
      CREATE OR REPLACE FUNCTION global_search(
        team_uuid uuid,
        search_query text,
        search_types text[] DEFAULT NULL,
        search_limit integer DEFAULT 10
      )
      RETURNS TABLE (
        id uuid,
        title text,
        content text,
        type text,
        date date,
        score real
      )
      LANGUAGE plpgsql
      AS $$
      BEGIN
        RETURN QUERY
        SELECT 
          d.id,
          d.title,
          d.body as content,
          'document' as type,
          d.date,
          1.0::real as score
        FROM documents d
        WHERE d.team_id = team_uuid
          AND (search_types IS NULL OR 'document' = ANY(search_types))
          AND (d.fts @@ plainto_tsquery('english', search_query) OR d.title ILIKE '%' || search_query || '%')
        
        UNION ALL
        
        SELECT 
          t.id,
          t.name as title,
          t.description as content,
          'transaction' as type,
          t.date,
          1.0::real as score
        FROM transactions t
        WHERE t.team_id = team_uuid
          AND (search_types IS NULL OR 'transaction' = ANY(search_types))
          AND (t.fts_vector @@ plainto_tsquery('english', search_query) OR t.name ILIKE '%' || search_query || '%')
        
        UNION ALL
        
        SELECT 
          c.id,
          c.name as title,
          c.email as content,
          'customer' as type,
          c.created_at::date as date,
          1.0::real as score
        FROM customers c
        WHERE c.team_id = team_uuid
          AND (search_types IS NULL OR 'customer' = ANY(search_types))
          AND (c.fts @@ plainto_tsquery('english', search_query) OR c.name ILIKE '%' || search_query || '%')
        
        ORDER BY score DESC, date DESC
        LIMIT search_limit;
      END;
      $$;
    `);
    
    // 3. Create global semantic search function (placeholder for now)
    console.log("3. Creating global_semantic_search function...");
    await db.execute(sql`
      CREATE OR REPLACE FUNCTION global_semantic_search(
        team_uuid uuid,
        search_query text,
        search_types text[] DEFAULT NULL,
        search_limit integer DEFAULT 10
      )
      RETURNS TABLE (
        id uuid,
        title text,
        content text,
        type text,
        date date,
        score real
      )
      LANGUAGE plpgsql
      AS $$
      BEGIN
        -- For now, fallback to regular search
        -- In production, this would use vector/semantic search
        RETURN QUERY
        SELECT * FROM global_search(team_uuid, search_query, search_types, search_limit);
      END;
      $$;
    `);
    
    // 4. Create generate_inbox function if it doesn't exist
    console.log("4. Creating generate_inbox function...");
    await db.execute(sql`
      CREATE OR REPLACE FUNCTION generate_inbox(length integer DEFAULT 10)
      RETURNS text
      LANGUAGE plpgsql
      AS $$
      DECLARE
        chars text := 'abcdefghijklmnopqrstuvwxyz0123456789';
        result text := '';
        i integer;
      BEGIN
        FOR i IN 1..length LOOP
          result := result || substr(chars, floor(random() * length(chars) + 1)::integer, 1);
        END LOOP;
        RETURN result;
      END;
      $$;
    `);
    
    // 5. Create inbox FTS function
    console.log("5. Creating generate_inbox_fts function...");
    await db.execute(sql`
      CREATE OR REPLACE FUNCTION generate_inbox_fts(display_name text, product_names text)
      RETURNS tsvector
      LANGUAGE plpgsql
      AS $$
      BEGIN
        RETURN to_tsvector('english', COALESCE(display_name, '') || ' ' || COALESCE(product_names, ''));
      END;
      $$;
    `);
    
    // 6. Create extract_product_names function
    console.log("6. Creating extract_product_names function...");
    await db.execute(sql`
      CREATE OR REPLACE FUNCTION extract_product_names(products_json text)
      RETURNS text
      LANGUAGE plpgsql
      AS $$
      DECLARE
        result text := '';
      BEGIN
        -- Simple extraction for now - in production you'd parse JSON properly
        IF products_json IS NOT NULL AND products_json != '' THEN
          result := regexp_replace(products_json, '[{}"\[\]]', ' ', 'g');
          result := regexp_replace(result, '[:,]', ' ', 'g');
        END IF;
        RETURN COALESCE(result, '');
      END;
      $$;
    `);
    
    // 7. Create nanoid function for user invites
    console.log("7. Creating nanoid function...");
    await db.execute(sql`
      CREATE OR REPLACE FUNCTION nanoid(length integer DEFAULT 21)
      RETURNS text
      LANGUAGE plpgsql
      AS $$
      DECLARE
        alphabet text := '_-0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        result text := '';
        i integer;
      BEGIN
        FOR i IN 1..length LOOP
          result := result || substr(alphabet, floor(random() * length(alphabet) + 1)::integer, 1);
        END LOOP;
        RETURN result;
      END;
      $$;
    `);
    
    // Test the functions
    console.log("8. Testing the functions...");
    
    const testSearch = await db.execute(sql`
      SELECT * FROM global_search('00000000-0000-0000-0000-000000000000'::uuid, 'test', NULL, 5)
    `);
    console.log("Global search test:", testSearch.length === 0 ? "✅ Working (no data)" : `✅ Working (${testSearch.length} results)`);
    
    const testInbox = await db.execute(sql`
      SELECT generate_inbox(10) as inbox_id
    `);
    console.log("Generate inbox test:", testInbox[0] ? "✅ Working" : "❌ Failed");
    
    const testNanoid = await db.execute(sql`
      SELECT nanoid(24) as nano_id
    `);
    console.log("Nanoid test:", testNanoid[0] ? "✅ Working" : "❌ Failed");
    
    console.log("✅ All missing functions created successfully!");
    
  } catch (error) {
    console.error("❌ Error creating functions:", error);
    process.exit(1);
  }
}

createMissingFunctions();