#!/usr/bin/env bun

import { connectDb, primaryDb } from "./src/db/index";
import { sql } from "drizzle-orm";

async function testDb() {
  console.log("Testing database connection...");

  // Check environment variables
  console.log("Environment variables:");
  console.log(
    "DATABASE_PRIMARY_URL exists:",
    !!process.env.DATABASE_PRIMARY_URL,
  );
  console.log("DATABASE_FRA_URL exists:", !!process.env.DATABASE_FRA_URL);
  console.log("DATABASE_SJC_URL exists:", !!process.env.DATABASE_SJC_URL);
  console.log("DATABASE_IAD_URL exists:", !!process.env.DATABASE_IAD_URL);
  console.log(
    "SUPABASE_SERVICE_KEY exists:",
    !!process.env.SUPABASE_SERVICE_KEY,
  );
  console.log("FLY_REGION:", process.env.FLY_REGION || "not set");

  try {
    // Test primary connection
    console.log("\n1. Testing primary database connection...");
    const result1 = await primaryDb.execute(sql`SELECT 1 as test`);
    console.log("Primary DB test result:", result1);

    // Test with connectDb function
    console.log("\n2. Testing connectDb function...");
    const db = await connectDb();
    const result2 = await db.execute(sql`SELECT 1 as test`);
    console.log("ConnectDb test result:", result2);

    // Test a simple query that should work
    console.log("\n3. Testing a simple table query...");
    const result3 = await db.execute(
      sql`SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' LIMIT 5`,
    );
    console.log("Tables query result:", result3);

    // Test the actual metric function if it exists
    console.log("\n4. Testing if get_profit_v3 function exists...");
    const result4 = await db.execute(sql`
      SELECT routine_name 
      FROM information_schema.routines 
      WHERE routine_schema = 'public' 
      AND routine_name LIKE 'get_%_v%'
      ORDER BY routine_name
    `);
    console.log("Available metric functions:", result4);

    console.log("\n✅ Database connection tests completed successfully!");
  } catch (error) {
    console.error("\n❌ Database connection failed:");
    console.error("Error:", error);

    if (error instanceof Error) {
      console.error("Message:", error.message);
      console.error("Stack:", error.stack);
    }
  }
}

testDb()
  .then(() => {
    console.log("Test completed");
    process.exit(0);
  })
  .catch((error) => {
    console.error("Test failed:", error);
    process.exit(1);
  });
