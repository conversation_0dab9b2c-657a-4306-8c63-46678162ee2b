import { relations } from "drizzle-orm/relations";
import { users, projectPermissions, trackerProjects, trackerEntries, teams, resources, projectTypes, projects, projectMembers, roles, usersInAuth, tasks, materials, invoices, customers, projectFiles, customerTags, tags, transactions, bankAccounts, resourceAllocations, reports, trackerReports, projectTrades, bankConnections, apps, documents, projectModels, trackerProjectTags, modelAnnotations, annotationThreads, transactionTags, transactionAttachments, projectTasks, projectPhases, bimModels, projectResources, projectTeamMembers, projectTemplates, safetyIncidents, changeOrders, projectCommunications, projectProgressLogs, projectInspections, documentTagAssignments, documentTags, usersOnTeam } from "./schema";

export const projectPermissionsRelations = relations(projectPermissions, ({one}) => ({
	user_grantedBy: one(users, {
		fields: [projectPermissions.grantedBy],
		references: [users.id],
		relationName: "projectPermissions_grantedBy_users_id"
	}),
	trackerProject: one(trackerProjects, {
		fields: [projectPermissions.projectId],
		references: [trackerProjects.id]
	}),
	user_userId: one(users, {
		fields: [projectPermissions.userId],
		references: [users.id],
		relationName: "projectPermissions_userId_users_id"
	}),
}));

export const usersRelations = relations(users, ({one, many}) => ({
	projectPermissions_grantedBy: many(projectPermissions, {
		relationName: "projectPermissions_grantedBy_users_id"
	}),
	projectPermissions_userId: many(projectPermissions, {
		relationName: "projectPermissions_userId_users_id"
	}),
	trackerEntries: many(trackerEntries),
	invoices: many(invoices),
	transactions: many(transactions),
	reports: many(reports),
	trackerReports: many(trackerReports),
	usersInAuth: one(usersInAuth, {
		fields: [users.id],
		references: [usersInAuth.id]
	}),
	projectTrades: many(projectTrades),
	bankAccounts: many(bankAccounts),
	apps: many(apps),
	documents: many(documents),
	projectModels: many(projectModels),
	annotationThreads: many(annotationThreads),
	modelAnnotations_assignedTo: many(modelAnnotations, {
		relationName: "modelAnnotations_assignedTo_users_id"
	}),
	modelAnnotations_resolvedBy: many(modelAnnotations, {
		relationName: "modelAnnotations_resolvedBy_users_id"
	}),
	modelAnnotations_userId: many(modelAnnotations, {
		relationName: "modelAnnotations_userId_users_id"
	}),
	projectTasks_assignedTo: many(projectTasks, {
		relationName: "projectTasks_assignedTo_users_id"
	}),
	projectTasks_createdBy: many(projectTasks, {
		relationName: "projectTasks_createdBy_users_id"
	}),
	projectTeamMembers: many(projectTeamMembers),
	projectTemplates: many(projectTemplates),
	safetyIncidents: many(safetyIncidents),
	changeOrders_approvedBy: many(changeOrders, {
		relationName: "changeOrders_approvedBy_users_id"
	}),
	changeOrders_requestedBy: many(changeOrders, {
		relationName: "changeOrders_requestedBy_users_id"
	}),
	projectCommunications: many(projectCommunications),
	projectProgressLogs: many(projectProgressLogs),
	trackerProjects: many(trackerProjects),
	projectInspections: many(projectInspections),
	usersOnTeams: many(usersOnTeam),
}));

export const trackerProjectsRelations = relations(trackerProjects, ({one, many}) => ({
	projectPermissions: many(projectPermissions),
	trackerEntries: many(trackerEntries),
	materials: many(materials),
	resourceAllocations: many(resourceAllocations),
	trackerReports: many(trackerReports),
	projectTrades: many(projectTrades),
	projectModels: many(projectModels),
	trackerProjectTags: many(trackerProjectTags),
	projectTasks: many(projectTasks),
	bimModels: many(bimModels),
	projectResources: many(projectResources),
	projectTeamMembers: many(projectTeamMembers),
	projectPhases: many(projectPhases),
	safetyIncidents: many(safetyIncidents),
	changeOrders: many(changeOrders),
	projectCommunications: many(projectCommunications),
	projectProgressLogs: many(projectProgressLogs),
	customer: one(customers, {
		fields: [trackerProjects.customerId],
		references: [customers.id]
	}),
	trackerProject: one(trackerProjects, {
		fields: [trackerProjects.parentProjectId],
		references: [trackerProjects.id],
		relationName: "trackerProjects_parentProjectId_trackerProjects_id"
	}),
	trackerProjects: many(trackerProjects, {
		relationName: "trackerProjects_parentProjectId_trackerProjects_id"
	}),
	user: one(users, {
		fields: [trackerProjects.projectManagerId],
		references: [users.id]
	}),
	projectTemplate: one(projectTemplates, {
		fields: [trackerProjects.templateId],
		references: [projectTemplates.id]
	}),
	projectInspections: many(projectInspections),
}));

export const trackerEntriesRelations = relations(trackerEntries, ({one}) => ({
	user: one(users, {
		fields: [trackerEntries.assignedId],
		references: [users.id]
	}),
	trackerProject: one(trackerProjects, {
		fields: [trackerEntries.projectId],
		references: [trackerProjects.id]
	}),
}));

export const resourcesRelations = relations(resources, ({one, many}) => ({
	team: one(teams, {
		fields: [resources.teamId],
		references: [teams.id]
	}),
	resourceAllocations: many(resourceAllocations),
}));

export const teamsRelations = relations(teams, ({many}) => ({
	resources: many(resources),
	materials: many(materials),
	resourceAllocations: many(resourceAllocations),
	projectTrades: many(projectTrades),
	bimModels: many(bimModels),
	projectTemplates: many(projectTemplates),
}));

export const projectsRelations = relations(projects, ({one, many}) => ({
	projectType: one(projectTypes, {
		fields: [projects.projectTypeId],
		references: [projectTypes.id]
	}),
	projectMembers: many(projectMembers),
	tasks: many(tasks),
	invoices: many(invoices),
	projectFiles: many(projectFiles),
}));

export const projectTypesRelations = relations(projectTypes, ({many}) => ({
	projects: many(projects),
}));

export const projectMembersRelations = relations(projectMembers, ({one, many}) => ({
	project: one(projects, {
		fields: [projectMembers.projectId],
		references: [projects.id]
	}),
	role: one(roles, {
		fields: [projectMembers.roleId],
		references: [roles.id]
	}),
	usersInAuth: one(usersInAuth, {
		fields: [projectMembers.userId],
		references: [usersInAuth.id]
	}),
	tasks: many(tasks),
}));

export const rolesRelations = relations(roles, ({many}) => ({
	projectMembers: many(projectMembers),
}));

export const usersInAuthRelations = relations(usersInAuth, ({many}) => ({
	projectMembers: many(projectMembers),
	projectFiles: many(projectFiles),
	users: many(users),
}));

export const tasksRelations = relations(tasks, ({one, many}) => ({
	projectMember: one(projectMembers, {
		fields: [tasks.assignedToMemberId],
		references: [projectMembers.id]
	}),
	task: one(tasks, {
		fields: [tasks.parentTaskId],
		references: [tasks.id],
		relationName: "tasks_parentTaskId_tasks_id"
	}),
	tasks: many(tasks, {
		relationName: "tasks_parentTaskId_tasks_id"
	}),
	project: one(projects, {
		fields: [tasks.projectId],
		references: [projects.id]
	}),
}));

export const materialsRelations = relations(materials, ({one}) => ({
	trackerProject: one(trackerProjects, {
		fields: [materials.projectId],
		references: [trackerProjects.id]
	}),
	team: one(teams, {
		fields: [materials.teamId],
		references: [teams.id]
	}),
}));

export const invoicesRelations = relations(invoices, ({one}) => ({
	user: one(users, {
		fields: [invoices.userId],
		references: [users.id]
	}),
	customer: one(customers, {
		fields: [invoices.customerId],
		references: [customers.id]
	}),
	project: one(projects, {
		fields: [invoices.projectId],
		references: [projects.id]
	}),
}));

export const customersRelations = relations(customers, ({many}) => ({
	invoices: many(invoices),
	customerTags: many(customerTags),
	trackerProjects: many(trackerProjects),
}));

export const projectFilesRelations = relations(projectFiles, ({one}) => ({
	project: one(projects, {
		fields: [projectFiles.projectId],
		references: [projects.id]
	}),
	usersInAuth: one(usersInAuth, {
		fields: [projectFiles.userId],
		references: [usersInAuth.id]
	}),
}));

export const customerTagsRelations = relations(customerTags, ({one}) => ({
	customer: one(customers, {
		fields: [customerTags.customerId],
		references: [customers.id]
	}),
	tag: one(tags, {
		fields: [customerTags.tagId],
		references: [tags.id]
	}),
}));

export const tagsRelations = relations(tags, ({many}) => ({
	customerTags: many(customerTags),
	trackerProjectTags: many(trackerProjectTags),
	transactionTags: many(transactionTags),
}));

export const transactionsRelations = relations(transactions, ({one, many}) => ({
	user: one(users, {
		fields: [transactions.assignedId],
		references: [users.id]
	}),
	bankAccount: one(bankAccounts, {
		fields: [transactions.bankAccountId],
		references: [bankAccounts.id]
	}),
	transactionTags: many(transactionTags),
	transactionAttachments: many(transactionAttachments),
}));

export const bankAccountsRelations = relations(bankAccounts, ({one, many}) => ({
	transactions: many(transactions),
	bankConnection: one(bankConnections, {
		fields: [bankAccounts.bankConnectionId],
		references: [bankConnections.id]
	}),
	user: one(users, {
		fields: [bankAccounts.createdBy],
		references: [users.id]
	}),
}));

export const resourceAllocationsRelations = relations(resourceAllocations, ({one}) => ({
	trackerProject: one(trackerProjects, {
		fields: [resourceAllocations.projectId],
		references: [trackerProjects.id]
	}),
	resource: one(resources, {
		fields: [resourceAllocations.resourceId],
		references: [resources.id]
	}),
	team: one(teams, {
		fields: [resourceAllocations.teamId],
		references: [teams.id]
	}),
}));

export const reportsRelations = relations(reports, ({one}) => ({
	user: one(users, {
		fields: [reports.createdBy],
		references: [users.id]
	}),
}));

export const trackerReportsRelations = relations(trackerReports, ({one}) => ({
	user: one(users, {
		fields: [trackerReports.createdBy],
		references: [users.id]
	}),
	trackerProject: one(trackerProjects, {
		fields: [trackerReports.projectId],
		references: [trackerProjects.id]
	}),
}));

export const projectTradesRelations = relations(projectTrades, ({one}) => ({
	team: one(teams, {
		fields: [projectTrades.assignedTeamId],
		references: [teams.id]
	}),
	user: one(users, {
		fields: [projectTrades.foremanId],
		references: [users.id]
	}),
	trackerProject: one(trackerProjects, {
		fields: [projectTrades.projectId],
		references: [trackerProjects.id]
	}),
}));

export const bankConnectionsRelations = relations(bankConnections, ({many}) => ({
	bankAccounts: many(bankAccounts),
}));

export const appsRelations = relations(apps, ({one}) => ({
	user: one(users, {
		fields: [apps.createdBy],
		references: [users.id]
	}),
}));

export const documentsRelations = relations(documents, ({one, many}) => ({
	user: one(users, {
		fields: [documents.ownerId],
		references: [users.id]
	}),
	bimModels: many(bimModels),
	documentTagAssignments: many(documentTagAssignments),
}));

export const projectModelsRelations = relations(projectModels, ({one, many}) => ({
	trackerProject: one(trackerProjects, {
		fields: [projectModels.projectId],
		references: [trackerProjects.id]
	}),
	user: one(users, {
		fields: [projectModels.uploadedBy],
		references: [users.id]
	}),
	modelAnnotations: many(modelAnnotations),
}));

export const trackerProjectTagsRelations = relations(trackerProjectTags, ({one}) => ({
	tag: one(tags, {
		fields: [trackerProjectTags.tagId],
		references: [tags.id]
	}),
	trackerProject: one(trackerProjects, {
		fields: [trackerProjectTags.trackerProjectId],
		references: [trackerProjects.id]
	}),
}));

export const annotationThreadsRelations = relations(annotationThreads, ({one}) => ({
	modelAnnotation: one(modelAnnotations, {
		fields: [annotationThreads.annotationId],
		references: [modelAnnotations.id]
	}),
	user: one(users, {
		fields: [annotationThreads.userId],
		references: [users.id]
	}),
}));

export const modelAnnotationsRelations = relations(modelAnnotations, ({one, many}) => ({
	annotationThreads: many(annotationThreads),
	user_assignedTo: one(users, {
		fields: [modelAnnotations.assignedTo],
		references: [users.id],
		relationName: "modelAnnotations_assignedTo_users_id"
	}),
	projectModel: one(projectModels, {
		fields: [modelAnnotations.modelId],
		references: [projectModels.id]
	}),
	user_resolvedBy: one(users, {
		fields: [modelAnnotations.resolvedBy],
		references: [users.id],
		relationName: "modelAnnotations_resolvedBy_users_id"
	}),
	user_userId: one(users, {
		fields: [modelAnnotations.userId],
		references: [users.id],
		relationName: "modelAnnotations_userId_users_id"
	}),
}));

export const transactionTagsRelations = relations(transactionTags, ({one}) => ({
	tag: one(tags, {
		fields: [transactionTags.tagId],
		references: [tags.id]
	}),
	transaction: one(transactions, {
		fields: [transactionTags.transactionId],
		references: [transactions.id]
	}),
}));

export const transactionAttachmentsRelations = relations(transactionAttachments, ({one}) => ({
	transaction: one(transactions, {
		fields: [transactionAttachments.transactionId],
		references: [transactions.id]
	}),
}));

export const projectTasksRelations = relations(projectTasks, ({one, many}) => ({
	user_assignedTo: one(users, {
		fields: [projectTasks.assignedTo],
		references: [users.id],
		relationName: "projectTasks_assignedTo_users_id"
	}),
	user_createdBy: one(users, {
		fields: [projectTasks.createdBy],
		references: [users.id],
		relationName: "projectTasks_createdBy_users_id"
	}),
	projectTask: one(projectTasks, {
		fields: [projectTasks.parentTaskId],
		references: [projectTasks.id],
		relationName: "projectTasks_parentTaskId_projectTasks_id"
	}),
	projectTasks: many(projectTasks, {
		relationName: "projectTasks_parentTaskId_projectTasks_id"
	}),
	projectPhase: one(projectPhases, {
		fields: [projectTasks.phaseId],
		references: [projectPhases.id]
	}),
	trackerProject: one(trackerProjects, {
		fields: [projectTasks.projectId],
		references: [trackerProjects.id]
	}),
	projectResources: many(projectResources),
	projectProgressLogs: many(projectProgressLogs),
	projectInspections: many(projectInspections),
}));

export const projectPhasesRelations = relations(projectPhases, ({one, many}) => ({
	projectTasks: many(projectTasks),
	trackerProject: one(trackerProjects, {
		fields: [projectPhases.projectId],
		references: [trackerProjects.id]
	}),
	projectProgressLogs: many(projectProgressLogs),
	projectInspections: many(projectInspections),
}));

export const bimModelsRelations = relations(bimModels, ({one}) => ({
	document: one(documents, {
		fields: [bimModels.documentId],
		references: [documents.id]
	}),
	trackerProject: one(trackerProjects, {
		fields: [bimModels.projectId],
		references: [trackerProjects.id]
	}),
	team: one(teams, {
		fields: [bimModels.teamId],
		references: [teams.id]
	}),
}));

export const projectResourcesRelations = relations(projectResources, ({one}) => ({
	trackerProject: one(trackerProjects, {
		fields: [projectResources.projectId],
		references: [trackerProjects.id]
	}),
	projectTask: one(projectTasks, {
		fields: [projectResources.taskId],
		references: [projectTasks.id]
	}),
}));

export const projectTeamMembersRelations = relations(projectTeamMembers, ({one}) => ({
	trackerProject: one(trackerProjects, {
		fields: [projectTeamMembers.projectId],
		references: [trackerProjects.id]
	}),
	user: one(users, {
		fields: [projectTeamMembers.userId],
		references: [users.id]
	}),
}));

export const projectTemplatesRelations = relations(projectTemplates, ({one, many}) => ({
	user: one(users, {
		fields: [projectTemplates.createdBy],
		references: [users.id]
	}),
	team: one(teams, {
		fields: [projectTemplates.teamId],
		references: [teams.id]
	}),
	trackerProjects: many(trackerProjects),
}));

export const safetyIncidentsRelations = relations(safetyIncidents, ({one}) => ({
	trackerProject: one(trackerProjects, {
		fields: [safetyIncidents.projectId],
		references: [trackerProjects.id]
	}),
	user: one(users, {
		fields: [safetyIncidents.reportedBy],
		references: [users.id]
	}),
}));

export const changeOrdersRelations = relations(changeOrders, ({one}) => ({
	user_approvedBy: one(users, {
		fields: [changeOrders.approvedBy],
		references: [users.id],
		relationName: "changeOrders_approvedBy_users_id"
	}),
	trackerProject: one(trackerProjects, {
		fields: [changeOrders.projectId],
		references: [trackerProjects.id]
	}),
	user_requestedBy: one(users, {
		fields: [changeOrders.requestedBy],
		references: [users.id],
		relationName: "changeOrders_requestedBy_users_id"
	}),
}));

export const projectCommunicationsRelations = relations(projectCommunications, ({one}) => ({
	trackerProject: one(trackerProjects, {
		fields: [projectCommunications.projectId],
		references: [trackerProjects.id]
	}),
	user: one(users, {
		fields: [projectCommunications.senderId],
		references: [users.id]
	}),
}));

export const projectProgressLogsRelations = relations(projectProgressLogs, ({one}) => ({
	user: one(users, {
		fields: [projectProgressLogs.loggedBy],
		references: [users.id]
	}),
	projectPhase: one(projectPhases, {
		fields: [projectProgressLogs.phaseId],
		references: [projectPhases.id]
	}),
	trackerProject: one(trackerProjects, {
		fields: [projectProgressLogs.projectId],
		references: [trackerProjects.id]
	}),
	projectTask: one(projectTasks, {
		fields: [projectProgressLogs.taskId],
		references: [projectTasks.id]
	}),
}));

export const projectInspectionsRelations = relations(projectInspections, ({one}) => ({
	user: one(users, {
		fields: [projectInspections.inspectorId],
		references: [users.id]
	}),
	projectPhase: one(projectPhases, {
		fields: [projectInspections.phaseId],
		references: [projectPhases.id]
	}),
	trackerProject: one(trackerProjects, {
		fields: [projectInspections.projectId],
		references: [trackerProjects.id]
	}),
	projectTask: one(projectTasks, {
		fields: [projectInspections.taskId],
		references: [projectTasks.id]
	}),
}));

export const documentTagAssignmentsRelations = relations(documentTagAssignments, ({one}) => ({
	document: one(documents, {
		fields: [documentTagAssignments.documentId],
		references: [documents.id]
	}),
	documentTag: one(documentTags, {
		fields: [documentTagAssignments.tagId],
		references: [documentTags.id]
	}),
}));

export const documentTagsRelations = relations(documentTags, ({many}) => ({
	documentTagAssignments: many(documentTagAssignments),
}));

export const usersOnTeamRelations = relations(usersOnTeam, ({one}) => ({
	user: one(users, {
		fields: [usersOnTeam.userId],
		references: [users.id]
	}),
}));