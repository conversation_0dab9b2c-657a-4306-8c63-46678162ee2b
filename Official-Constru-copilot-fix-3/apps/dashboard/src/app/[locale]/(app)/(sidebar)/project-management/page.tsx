"use client";

import { ProjectDashboardSkeleton } from "@/components/project-management/project-dashboard-skeleton";
import { ProjectManagementHub } from "@/components/project-management/project-management-hub";
import { useTeamQuery } from "@/hooks/use-team";
import { useUserQuery } from "@/hooks/use-user";
import { Alert, AlertDescription } from "@constru/ui/alert";
import { Button } from "@constru/ui/button";
import { <PERSON>ert<PERSON>riangle, Shield } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";

export default function ProjectManagementPage() {
  const router = useRouter();
  const { data: user, isLoading: userLoading } = useUserQuery();
  const { data: team, isLoading: teamLoading } = useTeamQuery();
  const [userRole, setUserRole] = useState<string>("worker");

  // Determine user role from team membership
  useEffect(() => {
    if (user && team) {
      // In a real app, this would come from the database
      // For now, we'll use a simple role assignment
      if (user.email?.includes("admin")) {
        setUserRole("admin");
      } else if (user.email?.includes("manager")) {
        setUserRole("project_manager");
      } else if (user.email?.includes("foreman")) {
        setUserRole("foreman");
      } else if (user.email?.includes("client")) {
        setUserRole("client");
      } else if (user.email?.includes("supervisor")) {
        setUserRole("supervisor");
      } else if (user.email?.includes("contractor")) {
        setUserRole("subcontractor");
      } else {
        setUserRole("worker");
      }
    }
  }, [user, team]);

  if (userLoading || teamLoading) {
    return <ProjectDashboardSkeleton />;
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-[600px]">
        <Alert className="max-w-md">
          <Shield className="h-4 w-4" />
          <AlertDescription>
            You must be logged in to access the project management hub.
          </AlertDescription>
          <Button className="mt-4 w-full" onClick={() => router.push("/login")}>
            Sign In
          </Button>
        </Alert>
      </div>
    );
  }

  if (!team) {
    return (
      <div className="flex items-center justify-center min-h-[600px]">
        <Alert className="max-w-md">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            You must be part of a team to access project management features.
            Please contact your administrator.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <ProjectManagementHub
      userRole={userRole as any}
      userId={user.id}
      teamId={team.id}
      userName={user.email || "User"}
    />
  );
}
