import { CreateProjectButton } from "@/components/project-management/create-project-button";
import { ProjectDashboard } from "@/components/project-management/project-dashboard";
import { ProjectFilters } from "@/components/project-management/project-filters";
import { ProjectStats } from "@/components/project-management/project-stats";
import { batchPrefetch, trpc } from "@/trpc/server";
import { getQueryClient } from "@/trpc/server";
import type { Metadata } from "next";
import type { SearchParams } from "nuqs";

export const metadata: Metadata = {
  title: "Projects | Constru",
  description: "Manage your construction projects, teams, and resources",
};

type Props = {
  searchParams: Promise<SearchParams>;
};

export default async function ProjectsPage(props: Props) {
  const queryClient = getQueryClient();
  const searchParams = await props.searchParams;

  // Extract filter parameters
  const status = searchParams.status as string;
  const projectType = searchParams.type as string;
  const priority = searchParams.priority as string;

  // Prefetch project data
  batchPrefetch([
    trpc.projectManagement.dashboard.queryOptions({
      projectIds: undefined,
      status: status as 'in_progress' | 'completed' | undefined,
      limit: 50,
    }),
    trpc.trackerProjects.get.queryOptions({
      status: status as any,
      pageSize: 20,
    }),
    trpc.customers.get.queryOptions({
      pageSize: 100,
    }),
    trpc.team.current.queryOptions(),
  ]);

  // Get projects data for stats
  const projects = await queryClient.fetchQuery(
    trpc.projectManagement.dashboard.queryOptions({
      projectIds: undefined,
      status: status as 'in_progress' | 'completed' | undefined,
      limit: 100,
    }),
  );

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Projects</h1>
          <p className="text-muted-foreground">
            Manage your construction projects and teams
          </p>
        </div>
        <CreateProjectButton />
      </div>

      {/* Stats Overview */}
      <ProjectStats projects={projects} />

      {/* Filters */}
      <ProjectFilters />

      {/* Project Dashboard */}
      <ProjectDashboard />
    </div>
  );
}
