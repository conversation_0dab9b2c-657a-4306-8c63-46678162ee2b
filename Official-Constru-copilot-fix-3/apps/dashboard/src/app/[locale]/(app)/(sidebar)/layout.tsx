import { ExportStatus } from "@/components/export-status";
import { <PERSON><PERSON> } from "@/components/header";
import { GlobalSheets } from "@/components/sheets/global-sheets";
import { Sidebar } from "@/components/sidebar";
import { TeamGuard } from "@/components/team-guard";
import {
  HydrateClient,
  batchPrefetch,
  getQueryClient,
  serverQuery,
} from "@/trpc/server";
import { getCountryCode, getCurrency } from "@constru/location";
import type { TRPCError } from "@trpc/server";
import { redirect } from "next/navigation";
import { Suspense } from "react";

export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  const queryClient = getQueryClient();
  const currencyPromise = getCurrency();
  const countryCodePromise = getCountryCode();

  // Fetch user data first to check authentication
  let user: any;
  try {
    user = await queryClient.fetchQuery(serverQuery.user.me.queryOptions());
  } catch (error) {
    // If user is not authenticated, redirect to login
    redirect("/login");
  }

  if (!user) {
    redirect("/login");
  }

  // Check if user has a team
  if (!user.teamId) {
    redirect("/setup");
  }

  // Only prefetch safe team-related data if user has a team
  try {
    await batchPrefetch([serverQuery.team.current.queryOptions()]);
  } catch (error) {
    // Handle team permission errors gracefully
    console.warn("Error prefetching team data:", error);
    const trpcError = error as TRPCError;
    if (
      trpcError?.code === "PRECONDITION_FAILED" ||
      trpcError?.code === "FORBIDDEN" ||
      trpcError?.message === "NO_TEAM_ASSIGNED" ||
      trpcError?.message === "No permission to access this team"
    ) {
      redirect("/setup");
    }
    // Don't re-throw - just log and continue
  }

  return (
    <HydrateClient>
      <TeamGuard>
        <div className="relative">
          <Sidebar />

          <div className="md:ml-[70px] pb-8">
            <Header />
            <div className="px-6">{children}</div>
          </div>

          <ExportStatus />

          <Suspense>
            <GlobalSheets
              currencyPromise={currencyPromise}
              countryCodePromise={countryCodePromise}
            />
          </Suspense>
        </div>
      </TeamGuard>
    </HydrateClient>
  );
}
