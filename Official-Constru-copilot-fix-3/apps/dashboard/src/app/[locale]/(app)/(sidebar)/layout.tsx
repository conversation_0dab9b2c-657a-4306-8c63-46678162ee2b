import { ExportStatus } from "@/components/export-status";
import { Head<PERSON> } from "@/components/header";
import { GlobalSheets } from "@/components/sheets/global-sheets";
import { Sidebar } from "@/components/sidebar";
import { TeamGuard } from "@/components/team-guard";
import {
  HydrateClient,
  batchPrefetch,
  getQueryClient,
  serverQuery,
} from "@/trpc/server";
import { getCountryCode, getCurrency } from "@constru/location";
import type { TRPCError } from "@trpc/server";
import { redirect } from "next/navigation";
import { Suspense } from "react";

export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  const queryClient = getQueryClient();
  const currencyPromise = getCurrency();
  const countryCodePromise = getCountryCode();

  // Auth checks are now handled by middleware
  // Only prefetch safe data that doesn't require authentication
  try {
    await batchPrefetch([
      // Only prefetch data that's safe to fail
    ]);
  } catch (error) {
    // Handle any prefetch errors gracefully
    console.warn("Error prefetching data:", error);
  }

  return (
    <HydrateClient>
      <TeamGuard>
        <div className="relative">
          <Sidebar />

          <div className="md:ml-[70px] pb-8">
            <Header />
            <div className="px-6">{children}</div>
          </div>

          <ExportStatus />

          <Suspense>
            <GlobalSheets
              currencyPromise={currencyPromise}
              countryCodePromise={countryCodePromise}
            />
          </Suspense>
        </div>
      </TeamGuard>
    </HydrateClient>
  );
}
