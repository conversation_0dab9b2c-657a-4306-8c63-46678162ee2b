import { ChartSelectors } from "@/components/charts/chart-selectors";
import { Charts } from "@/components/charts/charts";
import { EmptyState } from "@/components/charts/empty-state";
import { OverviewModal } from "@/components/modals/overview-modal";
import { CreateProjectButton } from "@/components/project-management/create-project-button";
import { ProjectDashboard } from "@/components/project-management/project-dashboard";
import { ProjectStats } from "@/components/project-management/project-stats";
import { Widgets } from "@/components/widgets";
import { defaultPeriod } from "@/components/widgets/spending/data";
import { loadMetricsParams } from "@/hooks/use-metrics-params";
import { HydrateClient, batchPrefetch, trpc } from "@/trpc/server";
import { getQueryClient } from "@/trpc/server";
import { Cookies } from "@/utils/constants";
import { Button } from "@constru/ui/button";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@constru/ui/card";
import { cn } from "@constru/ui/cn";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@constru/ui/tabs";
import {
  ArrowRight,
  BarChart3,
  Building2,
  Calendar,
  DollarSign,
  Users,
} from "lucide-react";
import type { Metadata } from "next";
import { cookies } from "next/headers";
import Link from "next/link";
import type { SearchParams } from "nuqs";

export const metadata: Metadata = {
  title: "Project Hub | Constru",
  description: "Comprehensive project management and business overview",
};

type Props = {
  searchParams: Promise<SearchParams>;
};

export default async function Overview(props: Props) {
  const queryClient = getQueryClient();
  const searchParams = await props.searchParams;
  const { from, to } = loadMetricsParams(searchParams);

  const cookieStore = await cookies();
  const hideConnectFlow =
    cookieStore.get(Cookies.HideConnectFlow)?.value === "true";

  // Enhanced prefetching for project management
  batchPrefetch([
    // Project Management Data
    trpc.projectManagement.dashboard.queryOptions({ 
      projectIds: undefined,
      status: undefined,
      limit: 20 
    }),
    trpc.trackerProjects.get.queryOptions({ pageSize: 10 }),
    trpc.customers.get.queryOptions({ pageSize: 50 }),
    trpc.team.current.queryOptions(),

    // Financial Data
    trpc.invoice.get.queryOptions({ pageSize: 10 }),
    trpc.invoice.paymentStatus.queryOptions(),
    trpc.metrics.expense.queryOptions({ from, to }),
    trpc.metrics.profit.queryOptions({ from, to }),
    trpc.metrics.burnRate.queryOptions({ from, to }),
    trpc.metrics.runway.queryOptions({ from, to }),
    trpc.bankAccounts.balances.queryOptions(),
    trpc.metrics.spending.queryOptions({
      from: defaultPeriod.from,
      to: defaultPeriod.to,
    }),

    // Other Data
    trpc.inbox.get.queryOptions(),
    trpc.documents.get.queryOptions({ pageSize: 10 }),
    trpc.transactions.get.queryOptions({ pageSize: 15 }),
  ]);

  // Preload project and financial data
  const [projects, accounts] = await Promise.all([
    queryClient.fetchQuery(
      trpc.projectManagement.dashboard.queryOptions({ 
        projectIds: undefined,
        status: undefined,
        limit: 20 
      }),
    ),
    queryClient.fetchQuery(
      trpc.bankAccounts.get.queryOptions({
        enabled: true,
      }),
    ),
    queryClient.fetchQuery(
      trpc.metrics.revenue.queryOptions({
        from,
        to,
      }),
    ),
  ]);

  const hasProjects = projects && projects.length > 0;
  const hasAccounts = accounts && accounts.length > 0;

  return (
    <HydrateClient>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Project Hub</h1>
            <p className="text-muted-foreground">
              Manage your construction projects and business operations
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button asChild variant="outline">
              <Link href="/projects">
                <Building2 className="mr-2 h-4 w-4" />
                All Projects
              </Link>
            </Button>
            <CreateProjectButton />
          </div>
        </div>

        {/* Quick Stats Cards */}
        {hasProjects && <ProjectStats projects={projects} />}

        {/* Main Content Tabs */}
        <Tabs defaultValue="projects" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="projects" className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Projects
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Analytics
            </TabsTrigger>
            <TabsTrigger value="team" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Team
            </TabsTrigger>
            <TabsTrigger value="financial" className="flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Financial
            </TabsTrigger>
          </TabsList>

          {/* Projects Tab */}
          <TabsContent value="projects" className="space-y-6">
            {hasProjects ? (
              <ProjectDashboard />
            ) : (
              <Card className="p-12 text-center">
                <div className="mx-auto w-48 h-48 mb-6 opacity-20">
                  <svg
                    viewBox="0 0 24 24"
                    fill="none"
                    className="w-full h-full"
                  >
                    <path
                      d="M3 7V17C3 18.1046 3.89543 19 5 19H19C20.1046 19 21 18.1046 21 17V7M3 7L12 2L21 7M3 7V9H21V7"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M8 11V15M12 11V15M16 11V15"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                    />
                  </svg>
                </div>
                <h3 className="text-2xl font-semibold mb-3">
                  Welcome to Constru
                </h3>
                <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                  Transform your construction business with comprehensive
                  project management, team collaboration, and financial
                  tracking.
                </p>
                <div className="flex items-center justify-center gap-4">
                  <CreateProjectButton />
                  <Button variant="outline" asChild>
                    <Link href="/projects">
                      <Calendar className="mr-2 h-4 w-4" />
                      View Projects
                    </Link>
                  </Button>
                </div>
              </Card>
            )}
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Charts Section */}
              <div className="lg:col-span-2 space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      Financial Analytics
                      <ChartSelectors />
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="relative">
                      {!hasAccounts && <EmptyState />}
                      <div
                        className={cn(!hasAccounts && "blur-[8px] opacity-20")}
                      >
                        <Charts disabled={!hasAccounts} />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Widgets Section */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Quick Insights</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Widgets disabled={false} />
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Team Tab */}
          <TabsContent value="team" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Team Overview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <Users className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="font-medium mb-2">Team Management</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Manage your team members, roles, and project assignments
                    </p>
                    <Button asChild>
                      <Link href="/team">
                        View Team
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Active Team Members</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <div className="text-3xl font-bold text-blue-600 mb-2">
                      {/* This would be populated with actual team data */}
                      {projects?.reduce(
                        (sum, p) => sum + (p.teamSize || 0),
                        0,
                      ) || 0}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Total team members across projects
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Project Assignments</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <div className="text-3xl font-bold text-green-600 mb-2">
                      {projects?.filter((p) => p.status === "in_progress")
                        .length || 0}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Active project assignments
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Financial Tab */}
          <TabsContent value="financial" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    Financial Overview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Widgets disabled={false} />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Banking & Transactions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <DollarSign className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="font-medium mb-2">Financial Management</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Track expenses, invoices, and banking integration
                    </p>
                    <div className="flex gap-2 justify-center">
                      <Button asChild variant="outline">
                        <Link href="/transactions">View Transactions</Link>
                      </Button>
                      <Button asChild>
                        <Link href="/invoices">
                          Invoices
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      <OverviewModal
        defaultOpen={!hasProjects && !hasAccounts && !hideConnectFlow}
      />
    </HydrateClient>
  );
}
