import { ProjectOverview } from "@/components/project/project-overview";
import { prefetch, trpc } from "@/trpc/server";
import type { Metadata } from "next";
import { notFound } from "next/navigation";

type Props = {
  params: Promise<{ id: string; locale: string }>;
};

export async function generateMetadata(props: Props): Promise<Metadata> {
  const params = await props.params;

  try {
    const project = await trpc.trackerProjects.getById({ id: params.id });
    return {
      title: `${project.name} | Constru`,
    };
  } catch {
    return {
      title: "Project | Constru",
    };
  }
}

export default async function ProjectPage(props: Props) {
  const params = await props.params;

  try {
    // Prefetch project data
    prefetch(trpc.trackerProjects.getById.queryOptions({ id: params.id }));

    // Prefetch project documents (for visualization)
    prefetch(
      trpc.documents.get.infiniteQueryOptions({
        q: null,
        tags: null,
        pageSize: 20,
      }),
    );

    return <ProjectOverview projectId={params.id} />;
  } catch {
    notFound();
  }
}
