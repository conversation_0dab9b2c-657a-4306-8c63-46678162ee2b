export default function TestPage() {
  return (
    <div style={{ 
      backgroundColor: 'white', 
      color: 'black', 
      padding: '50px',
      minHeight: '100vh'
    }}>
      <h1 style={{ fontSize: '48px', fontWeight: 'bold' }}>TEST PAGE</h1>
      <p style={{ fontSize: '24px' }}>If you can see this, the app is working!</p>
      <p style={{ marginTop: '20px' }}>Current time: {new Date().toISOString()}</p>
      <div style={{ marginTop: '40px', padding: '20px', backgroundColor: '#f0f0f0' }}>
        <p>Debug info:</p>
        <ul>
          <li>URL: {typeof window !== 'undefined' ? window.location.href : 'Server render'}</li>
          <li>Theme: {typeof document !== 'undefined' ? document.documentElement.className : 'Server render'}</li>
        </ul>
      </div>
    </div>
  );
}