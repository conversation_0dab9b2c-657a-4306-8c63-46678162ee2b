import { Cookies } from "@/utils/constants";
import { LogEvents } from "@constru/events/events";
import { setupAnalytics } from "@constru/events/server";
import { getSession } from "@constru/supabase/cached-queries";
import { createClient } from "@constru/supabase/server";
import { addYears } from "date-fns";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export async function GET(req: NextRequest) {
  const cookieStore = await cookies();
  const requestUrl = new URL(req.url);
  const code = requestUrl.searchParams.get("code");
  const client = requestUrl.searchParams.get("client");
  const returnTo = requestUrl.searchParams.get("return_to");
  const provider = requestUrl.searchParams.get("provider");
  const mfaSetupVisited = cookieStore.has(Cookies.MfaSetupVisited);

  if (client === "desktop") {
    return NextResponse.redirect(`${requestUrl.origin}/verify?code=${code}`);
  }

  if (provider) {
    cookieStore.set(Cookies.PreferredSignInProvider, provider, {
      expires: addYears(new Date(), 1),
    });
  }

  const supabase = await createClient();

  // Handle OAuth callback with code
  if (code) {
    await supabase.auth.exchangeCodeForSession(code);
  }

  // Get current session (works for both OAuth and OTP)
  const {
    data: { session },
  } = await getSession();

  if (session) {
    const userId = session.user.id;

    const analytics = await setupAnalytics({
      userId,
      fullName: session.user.user_metadata?.full_name,
    });

    await analytics.track({
      event: LogEvents.SignIn.name,
      channel: LogEvents.SignIn.channel,
    });

    // If user have no teams, redirect to team creation
    const { count } = await supabase
      .from("users_on_team")
      .select("*", { count: "exact" })
      .eq("user_id", userId);

    if (count === 0 && !returnTo?.startsWith("teams/invite/")) {
      const teamsCreateUrl = new URL("/en/teams/create", requestUrl.origin);
      return NextResponse.redirect(teamsCreateUrl);
    }
  }

  if (!mfaSetupVisited) {
    cookieStore.set(Cookies.MfaSetupVisited, "true", {
      expires: addYears(new Date(), 1),
    });

    const mfaSetupUrl = new URL("/en/mfa/setup", requestUrl.origin);
    return NextResponse.redirect(mfaSetupUrl);
  }

  if (returnTo) {
    const returnUrl = new URL(`/${returnTo}`, requestUrl.origin);
    return NextResponse.redirect(returnUrl);
  }

  const homeUrl = new URL("/en", requestUrl.origin);
  return NextResponse.redirect(homeUrl);
}
