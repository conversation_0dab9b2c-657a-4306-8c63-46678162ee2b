"use server";

import { Cookies } from "@/utils/constants";
import { createClient } from "@constru/supabase/server";
import { addYears } from "date-fns";
import { cookies } from "next/headers";
import { z } from "zod";
import { actionClient } from "./safe-action";

export const verifyOtpAction = actionClient
  .schema(
    z.object({
      token: z.string(),
      email: z.string(),
    }),
  )
  .action(async ({ parsedInput: { email, token } }) => {
    const supabase = await createClient();

    const { data, error } = await supabase.auth.verifyOtp({
      email,
      token,
      type: "email",
    });

    if (error) {
      throw new Error(error.message);
    }

    if (!data.session) {
      throw new Error("Failed to establish session after OTP verification");
    }

    // Set the preferred sign-in provider cookie
    (await cookies()).set(Cookies.PreferredSignInProvider, "otp", {
      expires: addYears(new Date(), 1),
    });

    // Ensure session is properly established
    await supabase.auth.getSession();

    return { success: true, session: data.session };
  });
