"use client";

import { useUserQuery } from "@/hooks/use-user";
import { useTRPC } from "@/trpc/client";
import { Avatar, AvatarFallback, AvatarImage } from "@constru/ui/avatar";
import { Button } from "@constru/ui/button";
import { Icons } from "@constru/ui/icons";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { AnimatePresence, motion } from "framer-motion";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";
import { useOnClickOutside } from "usehooks-ts";

export function TeamDropdown() {
  const ref = useRef<HTMLDivElement>(null);
  const { data: user } = useUserQuery();
  const queryClient = useQueryClient();

  const [selectedId, setSelectedId] = useState<string | undefined>(
    user?.team?.id,
  );
  const [isActive, setActive] = useState(false);
  const [isChangingTeam, setIsChangingTeam] = useState(false);

  const changeTeamMutation = useMutation(
    trpc.user.update.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries();
        setIsChangingTeam(false);
      },
    }),
  );

  const { data: teams } = useQuery(trpc.team.list.queryOptions());

  useEffect(() => {
    if (user?.team?.id) {
      setSelectedId(user.team.id);
    }
  }, [user?.team?.id]);

  const sortedTeams =
    teams?.sort((a, b) => {
      if (a.id === selectedId) return -1;
      if (b.id === selectedId) return 1;

      return (a.id ?? "").localeCompare(b.id ?? "");
    }) ?? [];

  // @ts-expect-error
  useOnClickOutside(ref, () => {
    if (!isChangingTeam) {
      setActive(false);
    }
  });

  const toggleActive = () => setActive((prev) => !prev);

  const handleTeamChange = (teamId: string) => {
    if (teamId === selectedId) {
      toggleActive();
      return;
    }

    setIsChangingTeam(true);
    setSelectedId(teamId);
    setActive(false);

    changeTeamMutation.mutate({ teamId });
  };

  return (
    <motion.div ref={ref} layout className="w-[32px] h-[32px] relative">
      <AnimatePresence>
        {isActive && (
          <motion.div
            className="w-[32px] h-[32px] left-0 overflow-hidden absolute"
            style={{ zIndex: 1 }}
            initial={{ y: 0, opacity: 0 }}
            animate={{ y: -(32 + 10) * sortedTeams.length, opacity: 1 }}
            transition={{
              type: "spring",
              stiffness: 400,
              damping: 25,
              mass: 1.2,
            }}
          >
            <Link href="/teams/create" onClick={() => setActive(false)}>
              <Button
                className="w-[32px] h-[32px]"
                size="icon"
                variant="outline"
              >
                <Icons.Add />
              </Button>
            </Link>
          </motion.div>
        )}
        {sortedTeams.map((team, index) => (
          <motion.div
            key={team.id}
            className="w-[32px] h-[32px] left-0 overflow-hidden absolute"
            style={{ zIndex: -index }}
            initial={{
              scale: `${100 - index * 16}%`,
              y: index * 5,
            }}
            animate={
              isActive
                ? {
                    y: -(32 + 10) * index,
                    scale: "100%",
                  }
                : {
                    scale: `${100 - index * 16}%`,
                    y: index * 5,
                  }
            }
            transition={{
              type: "spring",
              stiffness: 400,
              damping: 25,
              mass: 1.2,
            }}
          >
            <Avatar
              className="w-[32px] h-[32px] rounded-none border border-[#DCDAD2] dark:border-[#2C2C2C] cursor-pointer"
              onClick={() => {
                if (index === 0) {
                  toggleActive();
                } else {
                  handleTeamChange(team?.id ?? "");
                }
              }}
            >
              <AvatarImage
                src={team?.logoUrl ?? ""}
                alt={team?.name ?? ""}
                width={20}
                height={20}
                quality={100}
              />
              <AvatarFallback className="rounded-none w-[32px] h-[32px]">
                <span className="text-xs">
                  {team?.name?.charAt(0)?.toUpperCase()}
                  {team?.name?.charAt(1)?.toUpperCase()}
                </span>
              </AvatarFallback>
            </Avatar>
          </motion.div>
        ))}
      </AnimatePresence>
    </motion.div>
  );
}
