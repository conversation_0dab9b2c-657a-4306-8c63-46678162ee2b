"use client";

import { TrackerExportCSV } from "@/components/tracker-export-csv";
import { TrackerStatus } from "@/components/tracker-status";
import { useTrackerParams } from "@/hooks/use-tracker-params";
import { useUserQuery } from "@/hooks/use-user";
import { formatAmount, secondsToHoursAndMinutes } from "@/utils/format";
import { getWebsiteLogo } from "@/utils/logos";
import type { RouterOutputs } from "@api/trpc/routers/_app";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@constru/ui/alert-dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@constru/ui/avatar";
import { Badge } from "@constru/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@constru/ui/dropdown-menu";
import { Icons } from "@constru/ui/icons";
import { ScrollArea, ScrollBar } from "@constru/ui/scroll-area";
import { TableCell, TableRow } from "@constru/ui/table";
import Link from "next/link";

type DataTableCellProps = {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
};

export function DataTableCell({
  children,
  className,
  onClick,
}: DataTableCellProps) {
  return (
    <TableCell className={className} onClick={onClick}>
      {children}
    </TableCell>
  );
}

type RowProps = {
  children: React.ReactNode;
};

export function Row({ children }: RowProps) {
  return <TableRow className="h-[45px]">{children}</TableRow>;
}

type DataTableRowProps = {
  row: RouterOutputs["trackerProjects"]["get"]["data"][number];
  onDelete: ({ id }: { id: string }) => void;
};

export function DataTableRow({ row, onDelete }: DataTableRowProps) {
  const { setParams } = useTrackerParams();
  const { data: user } = useUserQuery();

  const onEditClick = () => {
    setParams({
      projectId: row.id,
      update: true,
    });
  };

  return (
    <AlertDialog>
      <DropdownMenu>
        <Row>
          <DataTableCell className="cursor-pointer">
            <Link href={`/tracker/${row.id}`} className="hover:underline">
              {row.name}
            </Link>
          </DataTableCell>
          <DataTableCell className="cursor-pointer">
            <Link href={`/tracker/${row.id}`}>
              {row.customer ? (
                <div className="flex items-center space-x-2">
                  <Avatar className="size-5">
                    {row.customer?.website && (
                      <AvatarImage
                        src={getWebsiteLogo(row.customer?.website)}
                        alt={`${row.customer?.name} logo`}
                        width={20}
                        height={20}
                        quality={100}
                      />
                    )}
                    <AvatarFallback className="text-[9px] font-medium">
                      {row.customer?.name?.[0]}
                    </AvatarFallback>
                  </Avatar>
                  <span className="truncate">{row.customer?.name}</span>
                </div>
              ) : (
                "-"
              )}
            </Link>
          </DataTableCell>

          <DataTableCell className="cursor-pointer">
            <Link href={`/tracker/${row.id}`}>
              <span className="text-sm">
                {row.estimate
                  ? `${secondsToHoursAndMinutes(row.totalDuration ?? 0)} / ${secondsToHoursAndMinutes(row.estimate * 3600)}`
                  : secondsToHoursAndMinutes(row.totalDuration ?? 0)}
              </span>
            </Link>
          </DataTableCell>
          <DataTableCell className="cursor-pointer">
            <Link href={`/tracker/${row.id}`}>
              {row.currency ? (
                <span className="text-sm">
                  {formatAmount({
                    currency: row.currency,
                    amount: row.totalAmount ?? 0,
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                    locale: user?.locale,
                  })}
                </span>
              ) : (
                "-"
              )}
            </Link>
          </DataTableCell>
          <DataTableCell className="cursor-pointer">
            <Link href={`/tracker/${row.id}`}>{row.description}</Link>
          </DataTableCell>
          <DataTableCell>
            <div className="relative">
              <ScrollArea className="w-[170px] whitespace-nowrap">
                <div className="flex items-center space-x-2">
                  {row.tags?.map((tag) => (
                    <Link href={`/transactions?tags=${tag.id}`} key={tag.id}>
                      <Badge
                        variant="tag-rounded"
                        className="whitespace-nowrap"
                      >
                        {tag.name}
                      </Badge>
                    </Link>
                  ))}
                </div>

                <ScrollBar orientation="horizontal" />
              </ScrollArea>

              <div className="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-background to-transparent pointer-events-none z-10" />
            </div>
          </DataTableCell>
          <DataTableCell className="cursor-pointer">
            <Link href={`/tracker/${row.id}`}>
              <div className="flex items-center space-x-2">
                {row.users?.map((user) => (
                  <Avatar key={user.id} className="size-4">
                    <AvatarImage
                      src={user.avatarUrl}
                      alt={user.fullName ?? ""}
                      width={20}
                      height={20}
                    />
                    <AvatarFallback className="text-[10px]">
                      {user.fullName?.slice(0, 1)?.toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                ))}
              </div>
            </Link>
          </DataTableCell>
          <DataTableCell>
            <div className="flex justify-between items-center">
              <Link href={`/tracker/${row.id}`}>
                <TrackerStatus status={row.status} />
              </Link>
              <DropdownMenuTrigger>
                <Icons.MoreHoriz />
              </DropdownMenuTrigger>
            </div>
          </DataTableCell>
        </Row>

        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete this
              project.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={() => onDelete({ id: row.id })}>
              Continue
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>

        <DropdownMenuContent className="w-42" sideOffset={10} align="end">
          <DropdownMenuItem onClick={onEditClick}>Edit</DropdownMenuItem>

          <TrackerExportCSV name={row.name} projectId={row.id} />

          <DropdownMenuSeparator />

          <AlertDialogTrigger asChild>
            <DropdownMenuItem className="text-destructive">
              Delete
            </DropdownMenuItem>
          </AlertDialogTrigger>
        </DropdownMenuContent>
      </DropdownMenu>
    </AlertDialog>
  );
}
