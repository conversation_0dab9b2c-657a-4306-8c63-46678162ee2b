"use client";

import { cn } from "@constru/ui/cn";
import { TableCell, TableRow } from "@constru/ui/table";
import { type Row, flexRender } from "@tanstack/react-table";
import type { Customer } from "./columns";

type Props = {
  row: Row<Customer>;
  setOpen: (id?: string) => void;
};

export function CustomerRow({ row, setOpen }: Props) {
  return (
    <>
      <TableRow
        className="hover:bg-transparent cursor-default h-[45px]"
        key={row.id}
      >
        {row.getVisibleCells().map((cell, index) => (
          <TableCell
            key={cell.id}
            onClick={() => ![3, 4, 5, 6].includes(index) && setOpen(row.id)}
            className={cn(index !== 0 && "hidden md:table-cell")}
          >
            {flexRender(cell.column.columnDef.cell, cell.getContext())}
          </TableCell>
        ))}
      </TableRow>
    </>
  );
}
