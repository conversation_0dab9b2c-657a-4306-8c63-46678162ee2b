"use client";

import type { RouterOutputs } from "@api/trpc/routers/_app";
import { Badge } from "@constru/ui/badge";
import { Button } from "@constru/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@constru/ui/card";
import { Icons } from "@constru/ui/icons";
import { useRef, useState } from "react";

type Props = {
  document: RouterOutputs["documents"]["get"]["data"][number];
  projectId: string;
};

type ViewMode = "view" | "annotate" | "measure";

function getFileFormat(filename: string): string {
  const ext = filename.split(".").pop()?.toLowerCase();
  return ext || "";
}

function is3D(filename: string): boolean {
  const format = getFileFormat(filename);
  return ["ifc", "rvt", "skp", "obj", "stl", "step", "iges", "3ds"].includes(
    format,
  );
}

function isImage(filename: string): boolean {
  const format = getFileFormat(filename);
  return ["jpg", "jpeg", "png", "svg"].includes(format);
}

export function ProjectViewer({ document, projectId }: Props) {
  const [viewMode, setViewMode] = useState<ViewMode>("view");
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [annotations, setAnnotations] = useState<
    Array<{
      id: string;
      x: number;
      y: number;
      title: string;
      type: string;
    }>
  >([]);

  const viewerRef = useRef<HTMLDivElement>(null);

  const filename =
    document.pathTokens?.[document.pathTokens.length - 1] || "Unknown";
  const is3DModel = is3D(filename);
  const isImageFile = isImage(filename);

  const handleViewerClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (viewMode !== "annotate") return;

    const rect = e.currentTarget.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;

    // Add new annotation
    const newAnnotation = {
      id: `ann_${Date.now()}`,
      x,
      y,
      title: `Annotation ${annotations.length + 1}`,
      type: "comment",
    };

    setAnnotations([...annotations, newAnnotation]);
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement && viewerRef.current) {
      viewerRef.current.requestFullscreen();
      setIsFullscreen(true);
    } else if (document.exitFullscreen) {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  return (
    <Card className="h-[600px] flex flex-col">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              {is3DModel ? (
                <Icons.Box className="h-5 w-5 text-blue-500" />
              ) : (
                <Icons.FileText className="h-5 w-5 text-green-500" />
              )}
              <CardTitle className="text-base">
                {document.title || filename}
              </CardTitle>
            </div>
            <Badge variant="outline" className="text-xs">
              {is3DModel ? "3D Model" : "2D Plan"}
            </Badge>
          </div>

          <div className="flex items-center space-x-2">
            {/* Tool Selection */}
            <div className="flex items-center border rounded-md">
              <Button
                variant={viewMode === "view" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("view")}
                className="rounded-r-none"
              >
                <Icons.Eye className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "annotate" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("annotate")}
                className="rounded-none border-x"
              >
                <Icons.MessageSquare className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "measure" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("measure")}
                className="rounded-l-none"
              >
                <Icons.Ruler className="h-4 w-4" />
              </Button>
            </div>

            <Button variant="outline" size="sm" onClick={toggleFullscreen}>
              <Icons.Maximize2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {viewMode === "annotate" && (
          <div className="text-sm text-muted-foreground">
            Click anywhere on the {is3DModel ? "model" : "plan"} to add
            annotations
          </div>
        )}
      </CardHeader>

      <CardContent className="flex-1 p-0 relative">
        <div
          ref={viewerRef}
          className="w-full h-full bg-muted/10 relative overflow-hidden cursor-crosshair"
          onClick={handleViewerClick}
        >
          {/* Viewer Content */}
          {is3DModel ? (
            // 3D Model Viewer
            <div className="w-full h-full bg-gradient-to-br from-slate-100 to-slate-200 flex items-center justify-center relative">
              {/* Mock 3D Viewer - Replace with actual 3D library */}
              <div className="text-center">
                <div className="relative">
                  {/* 3D Model Placeholder */}
                  <div className="w-64 h-64 mx-auto relative">
                    {/* Building representation */}
                    <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2">
                      {/* Base */}
                      <div className="w-32 h-4 bg-gray-400 rounded-sm transform perspective-1000 rotate-x-75" />
                      {/* Walls */}
                      <div className="w-32 h-20 bg-gray-300 border border-gray-400 transform -translate-y-1" />
                      {/* Roof */}
                      <div
                        className="w-36 h-6 bg-red-400 border border-red-500 transform -translate-y-21 -translate-x-2 clip-path-polygon"
                        style={{
                          clipPath:
                            "polygon(20% 0%, 80% 0%, 100% 100%, 0% 100%)",
                        }}
                      />
                    </div>
                  </div>

                  {/* 3D Controls */}
                  <div className="absolute bottom-4 left-4 bg-white/90 rounded-lg p-2 shadow-lg">
                    <div className="grid grid-cols-3 gap-1">
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <Icons.RotateCcw className="h-3 w-3" />
                      </Button>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <Icons.Home className="h-3 w-3" />
                      </Button>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <Icons.ZoomIn className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="mt-4 text-sm text-muted-foreground">
                  <Icons.Box className="h-4 w-4 mx-auto mb-2" />
                  <p>3D Model Viewer</p>
                  <p className="text-xs">
                    Use mouse to navigate • Click to annotate
                  </p>
                </div>
              </div>
            </div>
          ) : isImageFile ? (
            // Image/Plan Viewer
            <div className="w-full h-full bg-white flex items-center justify-center relative">
              {/* Blueprint/Plan placeholder */}
              <div className="w-full h-full max-w-4xl max-h-full p-8">
                <div className="w-full h-full border-2 border-blue-500 bg-blue-50 relative">
                  {/* Blueprint grid */}
                  <div
                    className="absolute inset-0 opacity-10"
                    style={{
                      backgroundImage: `
                        linear-gradient(to right, #3b82f6 1px, transparent 1px),
                        linear-gradient(to bottom, #3b82f6 1px, transparent 1px)
                      `,
                      backgroundSize: "20px 20px",
                    }}
                  />

                  {/* Blueprint content */}
                  <div className="absolute inset-4 border border-blue-400">
                    <div className="w-full h-full flex items-center justify-center">
                      <div className="text-center text-blue-600">
                        <Icons.FileText className="h-12 w-12 mx-auto mb-2" />
                        <p className="font-medium">Blueprint/Plan View</p>
                        <p className="text-sm opacity-75">{filename}</p>
                      </div>
                    </div>
                  </div>

                  {/* Title block */}
                  <div className="absolute bottom-4 right-4 bg-white border border-blue-400 p-2 text-xs">
                    <div className="grid grid-cols-2 gap-2 text-blue-600">
                      <div>Project: {projectId.slice(0, 8)}</div>
                      <div>Scale: 1:100</div>
                      <div>Date: {new Date().toLocaleDateString()}</div>
                      <div>Sheet: 1 of 1</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            // Generic file viewer
            <div className="w-full h-full bg-muted/10 flex items-center justify-center">
              <div className="text-center">
                <Icons.FileText className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">File Preview</h3>
                <p className="text-muted-foreground">{filename}</p>
                <p className="text-sm text-muted-foreground mt-2">
                  Preview not available for this file type
                </p>
              </div>
            </div>
          )}

          {/* Annotations Overlay */}
          {annotations.map((annotation) => (
            <div
              key={annotation.id}
              className="absolute w-6 h-6 bg-red-500 rounded-full border-2 border-white shadow-lg cursor-pointer hover:scale-110 transition-transform flex items-center justify-center"
              style={{
                left: `${annotation.x}%`,
                top: `${annotation.y}%`,
                transform: "translate(-50%, -50%)",
              }}
              title={annotation.title}
            >
              <span className="text-white text-xs font-bold">
                {annotations.indexOf(annotation) + 1}
              </span>
            </div>
          ))}

          {/* Mode Indicator */}
          {viewMode === "annotate" && (
            <div className="absolute top-4 left-4 bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-medium">
              <Icons.MessageSquare className="inline h-3 w-3 mr-1" />
              Annotation Mode
            </div>
          )}

          {viewMode === "measure" && (
            <div className="absolute top-4 left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
              <Icons.Ruler className="inline h-3 w-3 mr-1" />
              Measure Mode
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
