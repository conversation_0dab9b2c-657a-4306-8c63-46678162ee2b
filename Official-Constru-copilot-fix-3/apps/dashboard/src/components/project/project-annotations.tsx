"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@constru/ui/avatar";
import { Badge } from "@constru/ui/badge";
import { <PERSON><PERSON> } from "@constru/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@constru/ui/card";
import { Icons } from "@constru/ui/icons";
import { Input } from "@constru/ui/input";
import { ScrollArea } from "@constru/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@constru/ui/select";
import { Textarea } from "@constru/ui/textarea";
import { useState } from "react";

type Props = {
  documentId: string;
  projectId: string;
};

type Annotation = {
  id: string;
  title: string;
  description?: string;
  type: "comment" | "issue" | "rfi" | "inspection";
  status: "open" | "resolved" | "in_progress";
  priority?: "low" | "medium" | "high" | "critical";
  assignee?: {
    id: string;
    name: string;
    email: string;
    avatarUrl?: string;
  };
  user: {
    id: string;
    name: string;
    email: string;
    avatarUrl?: string;
  };
  createdAt: string;
  position?: {
    x: number;
    y: number;
  };
};

// Mock data for demo
const mockAnnotations: Annotation[] = [
  {
    id: "1",
    title: "Check structural beam alignment",
    description:
      "The main support beam appears to be slightly off center in the 3D model. Please verify measurements.",
    type: "issue",
    status: "open",
    priority: "high",
    user: {
      id: "user1",
      name: "John Smith",
      email: "<EMAIL>",
      avatarUrl: "/avatars/john.jpg",
    },
    assignee: {
      id: "user2",
      name: "Sarah Wilson",
      email: "<EMAIL>",
      avatarUrl: "/avatars/sarah.jpg",
    },
    createdAt: "2024-06-05T10:30:00Z",
    position: { x: 45, y: 32 },
  },
  {
    id: "2",
    title: "Material specification review",
    description:
      "Need to confirm the concrete grade for foundation. Drawing shows C25 but specs mention C30.",
    type: "rfi",
    status: "in_progress",
    priority: "medium",
    user: {
      id: "user3",
      name: "Mike Johnson",
      email: "<EMAIL>",
    },
    createdAt: "2024-06-05T09:15:00Z",
    position: { x: 78, y: 65 },
  },
  {
    id: "3",
    title: "Great progress on this section!",
    description:
      "The framing work looks excellent. Team is making good progress.",
    type: "comment",
    status: "open",
    user: {
      id: "user4",
      name: "Lisa Chen",
      email: "<EMAIL>",
    },
    createdAt: "2024-06-05T08:45:00Z",
    position: { x: 23, y: 89 },
  },
];

const typeColors = {
  comment: "bg-blue-500",
  issue: "bg-red-500",
  rfi: "bg-orange-500",
  inspection: "bg-green-500",
};

const priorityColors = {
  low: "border-green-200 text-green-700",
  medium: "border-yellow-200 text-yellow-700",
  high: "border-orange-200 text-orange-700",
  critical: "border-red-200 text-red-700",
};

export function ProjectAnnotations({ documentId, projectId }: Props) {
  const [annotations] = useState<Annotation[]>(mockAnnotations);
  const [newAnnotation, setNewAnnotation] = useState({
    title: "",
    description: "",
    type: "comment" as const,
    priority: "medium" as const,
  });
  const [isCreating, setIsCreating] = useState(false);

  const handleCreateAnnotation = () => {
    if (!newAnnotation.title.trim()) return;

    // Here you would call the API to create the annotation
    console.log("Creating annotation:", newAnnotation);

    // Reset form
    setNewAnnotation({
      title: "",
      description: "",
      type: "comment",
      priority: "medium",
    });
    setIsCreating(false);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60),
    );

    if (diffHours < 1) return "Just now";
    if (diffHours < 24) return `${diffHours}h ago`;
    return date.toLocaleDateString();
  };

  return (
    <Card className="h-[600px] flex flex-col">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base">Annotations</CardTitle>
          <Button
            size="sm"
            onClick={() => setIsCreating(!isCreating)}
            variant={isCreating ? "secondary" : "default"}
          >
            <Icons.Plus className="h-4 w-4 mr-1" />
            Add
          </Button>
        </div>
      </CardHeader>

      <CardContent className="flex-1 p-0 overflow-hidden flex flex-col">
        {/* Create Annotation Form */}
        {isCreating && (
          <div className="p-4 border-b bg-muted/20 space-y-3">
            <Input
              placeholder="Annotation title..."
              value={newAnnotation.title}
              onChange={(e) =>
                setNewAnnotation((prev) => ({ ...prev, title: e.target.value }))
              }
            />

            <Textarea
              placeholder="Description (optional)..."
              value={newAnnotation.description}
              onChange={(e) =>
                setNewAnnotation((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              rows={2}
            />

            <div className="grid grid-cols-2 gap-2">
              <Select
                value={newAnnotation.type}
                onValueChange={(value) =>
                  setNewAnnotation((prev) => ({ ...prev, type: value as any }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="comment">Comment</SelectItem>
                  <SelectItem value="issue">Issue</SelectItem>
                  <SelectItem value="rfi">RFI</SelectItem>
                  <SelectItem value="inspection">Inspection</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={newAnnotation.priority}
                onValueChange={(value) =>
                  setNewAnnotation((prev) => ({
                    ...prev,
                    priority: value as any,
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex space-x-2">
              <Button
                size="sm"
                onClick={handleCreateAnnotation}
                className="flex-1"
              >
                Create
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setIsCreating(false)}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}

        {/* Annotations List */}
        <ScrollArea className="flex-1">
          <div className="p-4 space-y-4">
            {annotations.length === 0 ? (
              <div className="text-center text-sm text-muted-foreground py-8">
                <Icons.MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No annotations yet</p>
                <p className="text-xs mt-1">
                  Click on the viewer to add annotations
                </p>
              </div>
            ) : (
              annotations.map((annotation) => (
                <div
                  key={annotation.id}
                  className="space-y-2 pb-4 border-b last:border-b-0"
                >
                  {/* Header */}
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-2">
                      <div
                        className={`w-3 h-3 rounded-full ${typeColors[annotation.type]}`}
                      />
                      <Badge variant="outline" className="text-xs">
                        {annotation.type.toUpperCase()}
                      </Badge>
                      {annotation.priority && annotation.type !== "comment" && (
                        <Badge
                          variant="outline"
                          className={`text-xs ${priorityColors[annotation.priority]}`}
                        >
                          {annotation.priority}
                        </Badge>
                      )}
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {formatDate(annotation.createdAt)}
                    </span>
                  </div>

                  {/* Content */}
                  <div>
                    <h4 className="text-sm font-medium mb-1">
                      {annotation.title}
                    </h4>
                    {annotation.description && (
                      <p className="text-sm text-muted-foreground mb-2">
                        {annotation.description}
                      </p>
                    )}
                  </div>

                  {/* User Info */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Avatar className="size-6">
                        {annotation.user.avatarUrl && (
                          <AvatarImage
                            src={annotation.user.avatarUrl}
                            alt={annotation.user.name}
                            width={24}
                            height={24}
                          />
                        )}
                        <AvatarFallback className="text-xs">
                          {annotation.user.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-xs text-muted-foreground">
                        {annotation.user.name}
                      </span>
                    </div>

                    {annotation.assignee && (
                      <div className="flex items-center space-x-1">
                        <Icons.User className="h-3 w-3 text-muted-foreground" />
                        <Avatar className="size-5">
                          {annotation.assignee.avatarUrl && (
                            <AvatarImage
                              src={annotation.assignee.avatarUrl}
                              alt={annotation.assignee.name}
                              width={20}
                              height={20}
                            />
                          )}
                          <AvatarFallback className="text-xs">
                            {annotation.assignee.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 px-2 text-xs"
                    >
                      <Icons.MessageCircle className="h-3 w-3 mr-1" />
                      Reply
                    </Button>
                    {annotation.type !== "comment" && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 px-2 text-xs"
                      >
                        <Icons.CheckCircle className="h-3 w-3 mr-1" />
                        Resolve
                      </Button>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
