"use client";

import { useTRPC } from "@/trpc/client";
import { formatAmount, secondsToHoursAndMinutes } from "@/utils/format";
import { getWebsiteLogo } from "@/utils/logos";
import { Avatar, AvatarFallback, AvatarImage } from "@constru/ui/avatar";
import { Badge } from "@constru/ui/badge";
import { Button } from "@constru/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@constru/ui/card";
import { Icons } from "@constru/ui/icons";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@constru/ui/tabs";
import { useQuery } from "@tanstack/react-query";
import Link from "next/link";
import { useState } from "react";
import { ProjectVisualization } from "./project-visualization";

type Props = {
  projectId: string;
};

export function ProjectOverview({ projectId }: Props) {

  const { data: project, isLoading } = useQuery(
    trpc.trackerProjects.getById.queryOptions({ id: projectId }),
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <Icons.Refresh className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!project) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <h3 className="text-lg font-semibold">Project not found</h3>
          <p className="text-muted-foreground">
            The project you're looking for doesn't exist.
          </p>
          <Button asChild className="mt-4">
            <Link href="/tracker">
              <Icons.ArrowBack className="mr-2 h-4 w-4" />
              Back to Projects
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/tracker">
              <Icons.ArrowBack className="h-4 w-4 mr-2" />
              Back to Projects
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{project.name}</h1>
            <div className="flex items-center space-x-2 mt-1">
              <Badge
                variant={
                  project.status === "completed" ? "default" : "secondary"
                }
              >
                {project.status}
              </Badge>
              {project.customer && (
                <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                  <Avatar className="size-4">
                    {project.customer.website && (
                      <AvatarImage
                        src={getWebsiteLogo(project.customer.website)}
                        alt={`${project.customer.name} logo`}
                        width={16}
                        height={16}
                        quality={100}
                      />
                    )}
                    <AvatarFallback className="text-[8px]">
                      {project.customer.name?.[0]}
                    </AvatarFallback>
                  </Avatar>
                  <span>{project.customer.name}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="visualization">Visualization</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {/* Duration Card */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Time
                </CardTitle>
                <Icons.Time className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {secondsToHoursAndMinutes(project.totalDuration || 0)}
                </div>
                {project.estimate && (
                  <p className="text-xs text-muted-foreground">
                    of {project.estimate}h estimated
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Revenue Card */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Revenue
                </CardTitle>
                <Icons.Currency className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatAmount({
                    currency: project.currency || "USD",
                    amount: project.totalAmount || 0,
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Team Size Card */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Team Members
                </CardTitle>
                <Icons.Face className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {project.users?.length || 0}
                </div>
              </CardContent>
            </Card>

            {/* Tags Card */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Tags</CardTitle>
                <Icons.Category className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {project.tags?.length || 0}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Project Details */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Project Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {project.description && (
                  <div>
                    <h4 className="text-sm font-medium mb-1">Description</h4>
                    <p className="text-sm text-muted-foreground">
                      {project.description}
                    </p>
                  </div>
                )}

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="text-sm font-medium mb-1">Created</h4>
                    <p className="text-sm text-muted-foreground">
                      {new Date(project.createdAt).toLocaleDateString()}
                    </p>
                  </div>

                  {project.currency && (
                    <div>
                      <h4 className="text-sm font-medium mb-1">Currency</h4>
                      <p className="text-sm text-muted-foreground">
                        {project.currency}
                      </p>
                    </div>
                  )}
                </div>

                {project.tags && project.tags.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium mb-2">Tags</h4>
                    <div className="flex flex-wrap gap-1">
                      {project.tags.map((tag) => (
                        <Badge
                          key={tag.id}
                          variant="secondary"
                          className="text-xs"
                        >
                          {tag.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Team Members */}
            {project.users && project.users.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Team Members</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {project.users.map((user) => (
                      <div
                        key={user.id}
                        className="flex items-center space-x-3"
                      >
                        <Avatar className="size-8">
                          <AvatarImage
                            src={user.avatarUrl}
                            alt={user.fullName}
                            width={32}
                            height={32}
                          />
                          <AvatarFallback className="text-xs">
                            {user.fullName
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="text-sm font-medium">{user.fullName}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="visualization">
          <ProjectVisualization projectId={projectId} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
