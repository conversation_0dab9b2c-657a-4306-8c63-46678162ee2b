"use client";

import { VaultUploadZone } from "@/components/vault/vault-upload-zone";
import { useTRPC } from "@/trpc/client";
import { Badge } from "@constru/ui/badge";
import { Button } from "@constru/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@constru/ui/card";
import { Icons } from "@constru/ui/icons";
import { Input } from "@constru/ui/input";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { ProjectAnnotations } from "./project-annotations";
import { ProjectViewer } from "./project-viewer";

type Props = {
  projectId: string;
};

// File types that can be visualized
const SUPPORTED_FORMATS = [
  // 2D/Blueprint formats
  "pdf",
  "dwg",
  "dxf",
  "jpg",
  "jpeg",
  "png",
  "svg",
  // 3D formats
  "ifc",
  "rvt",
  "skp",
  "obj",
  "stl",
  "step",
  "iges",
  "3ds",
];

const FORMAT_LABELS = {
  // 2D formats
  pdf: "PDF Plans",
  dwg: "AutoCAD Drawing",
  dxf: "AutoCAD Exchange",
  jpg: "Image",
  jpeg: "Image",
  png: "Image",
  svg: "Vector Drawing",
  // 3D formats
  ifc: "IFC Model",
  rvt: "Revit Model",
  skp: "SketchUp Model",
  obj: "OBJ Model",
  stl: "STL Model",
  step: "STEP Model",
  iges: "IGES Model",
  "3ds": "3DS Model",
};

function getFileFormat(filename: string): string {
  const ext = filename.split(".").pop()?.toLowerCase();
  return ext || "";
}

function isSupported(filename: string): boolean {
  const format = getFileFormat(filename);
  return SUPPORTED_FORMATS.includes(format);
}

function is3D(filename: string): boolean {
  const format = getFileFormat(filename);
  return ["ifc", "rvt", "skp", "obj", "stl", "step", "iges", "3ds"].includes(
    format,
  );
}

export function ProjectVisualization({ projectId }: Props) {
  const [selectedDocument, setSelectedDocument] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");

  // Get project documents that can be visualized
  const { data: documents, isLoading } = useQuery(
    trpc.documents.get.queryOptions({
      q: searchQuery || null,
      tags: null,
      pageSize: 50,
    }),
  );

  // Filter for visualization-ready documents
  const visualizationDocs =
    documents?.data?.filter(
      (doc) =>
        doc.pathTokens &&
        isSupported(doc.pathTokens[doc.pathTokens.length - 1]),
    ) || [];

  const selectedDoc = visualizationDocs.find(
    (doc) => doc.id === selectedDocument,
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <Icons.Refresh className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
      {/* Document List */}
      <div className="lg:col-span-1">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Project Files</CardTitle>
            <div className="relative">
              <Icons.Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search files..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="max-h-[500px] overflow-y-auto">
              {visualizationDocs.length === 0 ? (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  <Icons.Error className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No visualization files found</p>
                  <p className="text-xs mt-1">
                    Upload DWG, PDF, IFC, or other supported files
                  </p>
                </div>
              ) : (
                <div className="space-y-1">
                  {visualizationDocs.map((doc) => {
                    const filename =
                      doc.pathTokens?.[doc.pathTokens.length - 1] || "Unknown";
                    const format = getFileFormat(filename);
                    const isActive = selectedDocument === doc.id;

                    return (
                      <button
                        key={doc.id}
                        onClick={() => setSelectedDocument(doc.id)}
                        className={`w-full text-left p-3 border-b hover:bg-muted/50 transition-colors ${
                          isActive ? "bg-muted border-l-2 border-l-primary" : ""
                        }`}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2">
                              {is3D(filename) ? (
                                <Icons.Folder className="h-4 w-4 flex-shrink-0 text-blue-500" />
                              ) : (
                                <Icons.Description className="h-4 w-4 flex-shrink-0 text-green-500" />
                              )}
                              <span className="text-sm font-medium truncate">
                                {doc.title || filename}
                              </span>
                            </div>
                            <div className="flex items-center space-x-2 mt-1">
                              <Badge variant="outline" className="text-xs">
                                {FORMAT_LABELS[
                                  format as keyof typeof FORMAT_LABELS
                                ] || format.toUpperCase()}
                              </Badge>
                              {doc.metadata?.size && (
                                <span className="text-xs text-muted-foreground">
                                  {(doc.metadata.size / 1024 / 1024).toFixed(1)}
                                  MB
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </button>
                    );
                  })}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Upload Zone */}
        <div className="mt-4">
          <VaultUploadZone>
            <Card className="border-dashed border-2 hover:border-primary/50 transition-colors cursor-pointer">
              <CardContent className="p-6 text-center">
                <Icons.Add className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                <p className="text-sm text-muted-foreground">
                  Drop files here or click to upload
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  Supports DWG, PDF, IFC, OBJ, STL and more
                </p>
              </CardContent>
            </Card>
          </VaultUploadZone>
        </div>
      </div>

      {/* Viewer */}
      <div className="lg:col-span-2">
        {selectedDoc ? (
          <ProjectViewer document={selectedDoc} projectId={projectId} />
        ) : (
          <Card className="h-[600px] flex items-center justify-center">
            <div className="text-center">
              <Icons.Visibility className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
              <h3 className="text-lg font-semibold mb-2">
                Project Visualization
              </h3>
              <p className="text-muted-foreground mb-4">
                Select a blueprint or 3D model from the list to view and
                annotate
              </p>
              <div className="flex items-center justify-center space-x-6 text-sm text-muted-foreground">
                <div className="flex items-center space-x-2">
                  <Icons.Description className="h-4 w-4 text-green-500" />
                  <span>2D Plans</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Icons.Folder className="h-4 w-4 text-blue-500" />
                  <span>3D Models</span>
                </div>
              </div>
            </div>
          </Card>
        )}
      </div>

      {/* Annotations */}
      <div className="lg:col-span-1">
        {selectedDoc ? (
          <ProjectAnnotations
            documentId={selectedDoc.id}
            projectId={projectId}
          />
        ) : (
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Annotations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center text-sm text-muted-foreground">
                <Icons.Description className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>Select a file to view annotations</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
