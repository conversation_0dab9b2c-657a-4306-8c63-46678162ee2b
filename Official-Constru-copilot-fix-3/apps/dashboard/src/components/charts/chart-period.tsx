"use client";

import {
  chartPeriodOptions,
  useMetricsParams,
} from "@/hooks/use-metrics-params";
import { Button } from "@constru/ui/button";
import { Calendar } from "@constru/ui/calendar";
import { Icons } from "@constru/ui/icons";
import { Popover, PopoverContent, PopoverTrigger } from "@constru/ui/popover";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@constru/ui/select";
import { formatISO } from "date-fns";
import { formatDateRange } from "little-date";
import { useEffect, useState } from "react";
import type { DateRange } from "react-day-picker";

type Props = {
  disabled?: string;
};

export function ChartPeriod({ disabled }: Props) {
  const { params, setParams } = useMetricsParams();
  const [isMounted, setIsMounted] = useState(false);

  // Prevent hydration mismatch by only rendering after mounting
  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleChangePeriod = (
    range: DateRange | undefined,
    period?: string,
  ) => {
    const newRange = {
      from: range?.from
        ? formatISO(range.from, { representation: "date" })
        : params.from,
      to: range?.to
        ? formatISO(range.to, { representation: "date" })
        : params.to,
      period: period || params.period,
    };

    setParams(newRange);
  };

  // Handle calendar selection separately to match the expected type
  const handleCalendarSelect = (selectedRange: DateRange | undefined) => {
    handleChangePeriod(selectedRange);
  };

  // Format date range safely to prevent hydration mismatch
  const getFormattedDateRange = () => {
    if (!isMounted || !params.from || !params.to) {
      return "Select date range";
    }

    try {
      const fromDate = new Date(params.from);
      const toDate = new Date(params.to);

      // Use consistent formatting to prevent hydration issues
      const fromFormatted = fromDate.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "2-digit",
      });

      const toFormatted = toDate.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "2-digit",
      });

      return `${fromFormatted} - ${toFormatted}`;
    } catch (error) {
      return "Select date range";
    }
  };

  return (
    <div className="flex space-x-4">
      <Popover>
        <PopoverTrigger asChild disabled={Boolean(disabled)}>
          <Button
            variant="outline"
            className="justify-start text-left font-medium space-x-2"
          >
            <span className="line-clamp-1 text-ellipsis">
              {getFormattedDateRange()}
            </span>
            <Icons.ChevronDown />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-screen md:w-[550px] p-0 flex-col flex space-y-4"
          align="end"
          sideOffset={10}
        >
          <div className="p-4 pb-0">
            <Select
              value={params.period ?? undefined}
              onValueChange={(value) =>
                handleChangePeriod(
                  chartPeriodOptions.find((p) => p.value === value)?.range,
                  value,
                )
              }
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a period" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  {chartPeriodOptions.map((period) => (
                    <SelectItem key={period.value} value={period.value}>
                      {period.label}
                    </SelectItem>
                  ))}
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          <Calendar
            mode="range"
            numberOfMonths={2}
            selected={{
              from: params.from ? new Date(params.from) : undefined,
              to: params.to ? new Date(params.to) : undefined,
            }}
            defaultMonth={
              params.from
                ? new Date(params.from)
                : new Date(new Date().setMonth(new Date().getMonth() - 1))
            }
            initialFocus
            toDate={new Date()}
            onSelect={handleCalendarSelect}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}
