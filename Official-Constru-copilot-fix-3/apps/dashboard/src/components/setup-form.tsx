"use client";

import { useZodForm } from "@/hooks/use-zod-form";
import { useTRPC } from "@/trpc/client";
import { getCurrencyFromLocale } from "@constru/location";
import { Button } from "@constru/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@constru/ui/form";
import { Input } from "@constru/ui/input";
import { SubmitButton } from "@constru/ui/submit-button";
import {
  useMutation,
  useQueryClient,
  useSuspenseQuery,
} from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { z } from "zod";
import { AvatarUpload } from "./avatar-upload";

const formSchema = z.object({
  fullName: z.string().min(2).max(32),
  teamName: z.string().min(2).max(50).optional(),
});

export function SetupForm() {
  const router = useRouter();
  const uploadRef = useRef<HTMLInputElement>(null);
  const [currency, setCurrency] = useState<string>("USD");

  const { data: user } = useSuspenseQuery(trpc.user.me.queryOptions());
  const queryClient = useQueryClient();

  useEffect(() => {
    // Get currency based on browser locale
    const detectedCurrency = getCurrencyFromLocale();
    setCurrency(detectedCurrency);
  }, []);

  const form = useZodForm(formSchema, {
    defaultValues: {
      fullName: user?.fullName ?? "",
      teamName: "My Team",
    },
  });

  const updateUserMutation = useMutation(
    trpc.user.update.mutationOptions({
      onSuccess: async () => {
        // Check if user already has a team
        if (!user?.teamId) {
          // Create a team for the user
          createTeamMutation.mutate({
            name: form.getValues().teamName || "My Team",
            baseCurrency: currency,
          });
        } else {
          // User already has a team, just redirect
          router.replace("/");
        }
      },
      onError: (error) => {
        console.error("Failed to update user:", error);
        form.setError("fullName", {
          type: "manual",
          message: "Failed to save. Please try again.",
        });
      },
    }),
  );

  const createTeamMutation = useMutation(
    trpc.team.create.mutationOptions({
      onSuccess: () => {
        // Invalidate queries and redirect
        queryClient.invalidateQueries({ queryKey: ["user", "me"] });
        router.replace("/");
      },
      onError: (error) => {
        console.error("Failed to create team:", error);
        form.setError("teamName", {
          type: "manual",
          message: "Failed to create team. Please try again.",
        });
      },
    }),
  );

  function handleSubmit(data: z.infer<typeof formSchema>) {
    updateUserMutation.mutate({ fullName: data.fullName });
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
        <div className="flex justify-between items-end gap-4">
          <AvatarUpload
            userId={user?.id ?? ""}
            avatarUrl={user?.avatarUrl}
            size={80}
            ref={uploadRef}
          />
          <Button
            variant="outline"
            type="button"
            onClick={() => uploadRef.current?.click()}
          >
            Upload
          </Button>
        </div>

        <FormField
          control={form.control}
          name="fullName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Full name</FormLabel>
              <FormControl>
                <Input placeholder="John Doe" {...field} />
              </FormControl>
              <FormDescription>
                This is your first and last name.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {!user?.teamId && (
          <FormField
            control={form.control}
            name="teamName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Team name</FormLabel>
                <FormControl>
                  <Input placeholder="My Team" {...field} />
                </FormControl>
                <FormDescription>
                  You can change this later in team settings.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <SubmitButton
          type="submit"
          className="w-full"
          isSubmitting={
            updateUserMutation.isPending || createTeamMutation.isPending
          }
        >
          Save
        </SubmitButton>
      </form>
    </Form>
  );
}
