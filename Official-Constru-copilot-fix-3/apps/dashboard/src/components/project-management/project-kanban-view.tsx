"use client";

import { formatAmount } from "@/utils/format";
import { Avatar, AvatarFallback, AvatarImage } from "@constru/ui/avatar";
import { Badge } from "@constru/ui/badge";
import { Button } from "@constru/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@constru/ui/card";
import { cn } from "@constru/ui/cn";
import { Progress } from "@constru/ui/progress";
import { format } from "date-fns";
import {
  Building2,
  Calendar,
  DollarSign,
  ExternalLink,
  MapPin,
  Plus,
  Users,
} from "lucide-react";
import Link from "next/link";

interface ProjectKanbanViewProps {
  projects: Array<{
    id: string;
    name: string;
    description?: string;
    status: string;
    projectType?: string;
    priority?: string;
    completionPercentage?: number;
    budget?: number;
    actualCost?: number;
    plannedEndDate?: string;
    locationAddress?: string;
    totalTasks?: number;
    completedTasks?: number;
    teamSize?: number;
    customer?: {
      name: string;
    };
    projectManager?: {
      fullName: string;
      avatarUrl?: string;
    };
  }>;
  userRole?: string;
}

const statusColumns = [
  {
    key: "not_started",
    title: "Not Started",
    color: "border-gray-200 bg-gray-50",
    headerColor: "text-gray-700",
  },
  {
    key: "in_progress",
    title: "In Progress",
    color: "border-blue-200 bg-blue-50",
    headerColor: "text-blue-700",
  },
  {
    key: "on_hold",
    title: "On Hold",
    color: "border-yellow-200 bg-yellow-50",
    headerColor: "text-yellow-700",
  },
  {
    key: "completed",
    title: "Completed",
    color: "border-green-200 bg-green-50",
    headerColor: "text-green-700",
  },
];

export function ProjectKanbanView({
  projects,
  userRole,
}: ProjectKanbanViewProps) {
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "critical":
        return "border-l-red-500";
      case "high":
        return "border-l-orange-500";
      case "medium":
        return "border-l-yellow-500";
      case "low":
        return "border-l-green-500";
      default:
        return "border-l-gray-300";
    }
  };

  const getProjectsByStatus = (status: string) => {
    return projects.filter((project) => project.status === status);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 h-[calc(100vh-400px)]">
      {statusColumns.map((column) => {
        const columnProjects = getProjectsByStatus(column.key);

        return (
          <div key={column.key} className="flex flex-col">
            {/* Column Header */}
            <Card className={cn("mb-4", column.color)}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle
                    className={cn("text-sm font-medium", column.headerColor)}
                  >
                    {column.title}
                  </CardTitle>
                  <Badge variant="secondary" className="text-xs">
                    {columnProjects.length}
                  </Badge>
                </div>
              </CardHeader>
            </Card>

            {/* Column Content */}
            <div className="flex-1 space-y-3 overflow-y-auto">
              {columnProjects.map((project) => (
                <Card
                  key={project.id}
                  className={cn(
                    "border-l-4 hover:shadow-md transition-shadow cursor-pointer",
                    getPriorityColor(project.priority || "medium"),
                  )}
                >
                  <CardContent className="p-4 space-y-3">
                    {/* Project Header */}
                    <div className="space-y-2">
                      <Link
                        href={`/projects/${project.id}`}
                        className="font-medium hover:text-blue-600 transition-colors line-clamp-2"
                      >
                        {project.name}
                      </Link>

                      {project.description && (
                        <p className="text-xs text-muted-foreground line-clamp-2">
                          {project.description}
                        </p>
                      )}
                    </div>

                    {/* Customer and Location */}
                    <div className="space-y-1">
                      {project.customer && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Building2 className="h-3 w-3" />
                          <span className="truncate">
                            {project.customer.name}
                          </span>
                        </div>
                      )}

                      {project.locationAddress && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <MapPin className="h-3 w-3" />
                          <span className="truncate">
                            {project.locationAddress}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Progress */}
                    {project.completionPercentage !== undefined && (
                      <div className="space-y-1">
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-muted-foreground">
                            Progress
                          </span>
                          <span className="font-medium">
                            {project.completionPercentage}%
                          </span>
                        </div>
                        <Progress
                          value={project.completionPercentage}
                          className="h-1.5"
                        />
                      </div>
                    )}

                    {/* Timeline */}
                    {project.plannedEndDate && (
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Calendar className="h-3 w-3" />
                        <span>
                          Due{" "}
                          {format(new Date(project.plannedEndDate), "MMM dd")}
                        </span>
                      </div>
                    )}

                    {/* Stats Row */}
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      {/* Tasks */}
                      {(project.totalTasks || 0) > 0 && (
                        <div className="text-center p-1 bg-muted rounded">
                          <div className="font-medium">
                            {project.completedTasks || 0}/
                            {project.totalTasks || 0}
                          </div>
                          <div className="text-muted-foreground">Tasks</div>
                        </div>
                      )}

                      {/* Budget */}
                      {project.actualCost && (
                        <div className="text-center p-1 bg-muted rounded">
                          <div className="font-medium">
                            {formatAmount({
                              amount: project.actualCost,
                              currency: "USD",
                            })}
                          </div>
                          <div className="text-muted-foreground">Spent</div>
                        </div>
                      )}
                    </div>

                    {/* Team */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {project.projectManager && (
                          <Avatar className="h-6 w-6">
                            <AvatarImage
                              src={project.projectManager.avatarUrl}
                            />
                            <AvatarFallback className="text-xs">
                              {project.projectManager.fullName
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                        )}

                        {(project.teamSize || 0) > 1 && (
                          <span className="text-xs text-muted-foreground">
                            +{(project.teamSize || 1) - 1}
                          </span>
                        )}
                      </div>

                      {/* Priority Indicator */}
                      {project.priority && (
                        <div className="flex items-center gap-1">
                          <div
                            className={cn(
                              "w-2 h-2 rounded-full",
                              project.priority === "critical"
                                ? "bg-red-500"
                                : project.priority === "high"
                                  ? "bg-orange-500"
                                  : project.priority === "medium"
                                    ? "bg-yellow-500"
                                    : "bg-green-500",
                            )}
                          />
                        </div>
                      )}
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-1 pt-2 border-t">
                      <Button
                        asChild
                        size="sm"
                        variant="outline"
                        className="flex-1 text-xs"
                      >
                        <Link href={`/projects/${project.id}`}>
                          <ExternalLink className="mr-1 h-3 w-3" />
                          View
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {/* Add Project Button for empty columns */}
              {columnProjects.length === 0 && (
                <Card className="border-dashed">
                  <CardContent className="p-4 text-center">
                    <div className="text-muted-foreground text-sm">
                      No projects in {column.title.toLowerCase()}
                    </div>
                    {column.key === "not_started" && (
                      <Button variant="ghost" size="sm" className="mt-2">
                        <Plus className="mr-1 h-3 w-3" />
                        Add Project
                      </Button>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
}
