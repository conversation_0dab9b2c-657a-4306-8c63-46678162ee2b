"use client";

import { Badge } from "@constru/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@constru/ui/card";
import { UserPlus, Users } from "lucide-react";

interface TeamManagementViewProps {
  userRole: string;
  teamId: string;
  permissions: any;
}

export function TeamManagementView({
  userRole,
  teamId,
  permissions,
}: TeamManagementViewProps) {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Users className="h-6 w-6 text-primary" />
          <div>
            <h2 className="text-2xl font-bold">Team Management</h2>
            <p className="text-muted-foreground">
              Manage project teams and roles
            </p>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Team Management System</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <UserPlus className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              Team Management Coming Soon
            </h3>
            <p className="text-muted-foreground">
              Advanced team assignment, role management, and workload
              distribution features.
            </p>
            <Badge variant="secondary" className="mt-4">
              In Development
            </Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
