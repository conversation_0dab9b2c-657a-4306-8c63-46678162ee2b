"use client";

import { Badge } from "@constru/ui/badge";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@constru/ui/card";
import { MessageCircle, MessageSquare } from "lucide-react";

interface CommunicationHubProps {
  userRole: string;
  userId: string;
  teamId: string;
  permissions: any;
}

export function CommunicationHub({
  userRole,
  userId,
  teamId,
  permissions,
}: CommunicationHubProps) {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <MessageSquare className="h-6 w-6 text-primary" />
          <div>
            <h2 className="text-2xl font-bold">Communication Hub</h2>
            <p className="text-muted-foreground">
              Team messaging and project updates
            </p>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Project Communications</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <MessageCircle className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              Communication Hub Coming Soon
            </h3>
            <p className="text-muted-foreground">
              Real-time messaging, project updates, and team collaboration
              tools.
            </p>
            <Badge variant="secondary" className="mt-4">
              In Development
            </Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
