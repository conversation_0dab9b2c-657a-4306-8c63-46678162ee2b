"use client";

import { formatAmount } from "@/utils/format";
import { Avatar, AvatarFallback, AvatarImage } from "@constru/ui/avatar";
import { Badge } from "@constru/ui/badge";
import { Button } from "@constru/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@constru/ui/card";
import { cn } from "@constru/ui/cn";
import { Progress } from "@constru/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@constru/ui/select";
import { Separator } from "@constru/ui/separator";
import {
  addDays,
  differenceInDays,
  endOfWeek,
  format,
  startOfWeek,
} from "date-fns";
import { motion } from "framer-motion";
import {
  Activity,
  AlertCircle,
  AlertTriangle,
  ArrowDownRight,
  ArrowUpRight,
  BarChart3,
  Building2,
  Calendar,
  CheckCircle2,
  ChevronRight,
  CircleDollarSign,
  ClipboardCheck,
  Clock,
  DollarSign,
  FileText,
  Gauge,
  Hammer,
  MapPin,
  Minus,
  MoreVertical,
  Package,
  Percent,
  Shield,
  Sparkles,
  Target,
  Timer,
  TrendingDown,
  TrendingUp,
  Truck,
  UserCheck,
  Users,
  XCircle,
  Zap,
} from "lucide-react";
import { useMemo, useState } from "react";
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  Cell,
  Line,
  LineChart,
  Pie,
  PieChart,
  RadialBar,
  RadialBarChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

interface ProjectOverviewDashboardProps {
  userRole: string;
  userId: string;
  teamId: string;
  permissions: {
    canViewFinancials: boolean;
    canManageTeam: boolean;
    canAccessAnalytics: boolean;
  };
}

// Mock data - in production this would come from API
const generateMockData = () => {
  const projects = [
    {
      id: "1",
      name: "Downtown Office Tower",
      type: "commercial_construction",
      status: "in_progress",
      priority: "high",
      completionPercentage: 68,
      budget: 5500000,
      actualCost: 3740000,
      plannedStartDate: new Date("2024-01-15"),
      plannedEndDate: new Date("2024-12-31"),
      actualStartDate: new Date("2024-01-20"),
      projectManager: { name: "John Smith", avatar: "/avatars/john.jpg" },
      teamSize: 45,
      location: "Downtown District",
      healthScore: 85,
      overdueTasks: 3,
      totalTasks: 156,
      completedTasks: 106,
      activePhase: "Structural Work",
      weatherRisk: "low",
    },
    {
      id: "2",
      name: "Riverside Residential Complex",
      type: "residential_construction",
      status: "in_progress",
      priority: "critical",
      completionPercentage: 42,
      budget: 8200000,
      actualCost: 3444000,
      plannedStartDate: new Date("2024-03-01"),
      plannedEndDate: new Date("2025-06-30"),
      actualStartDate: new Date("2024-03-05"),
      projectManager: { name: "Sarah Johnson", avatar: "/avatars/sarah.jpg" },
      teamSize: 62,
      location: "Riverside District",
      healthScore: 72,
      overdueTasks: 8,
      totalTasks: 234,
      completedTasks: 98,
      activePhase: "Foundation Work",
      weatherRisk: "medium",
    },
    {
      id: "3",
      name: "Highway Bridge Renovation",
      type: "infrastructure",
      status: "in_progress",
      priority: "high",
      completionPercentage: 81,
      budget: 3200000,
      actualCost: 2592000,
      plannedStartDate: new Date("2023-11-01"),
      plannedEndDate: new Date("2024-08-31"),
      actualStartDate: new Date("2023-11-01"),
      projectManager: { name: "Mike Chen", avatar: "/avatars/mike.jpg" },
      teamSize: 28,
      location: "Highway 101",
      healthScore: 92,
      overdueTasks: 0,
      totalTasks: 89,
      completedTasks: 72,
      activePhase: "Final Inspections",
      weatherRisk: "high",
    },
  ];

  // Calculate aggregated metrics
  const totalBudget = projects.reduce((sum, p) => sum + p.budget, 0);
  const totalSpent = projects.reduce((sum, p) => sum + p.actualCost, 0);
  const totalTasks = projects.reduce((sum, p) => sum + p.totalTasks, 0);
  const completedTasks = projects.reduce((sum, p) => sum + p.completedTasks, 0);
  const overdueTasks = projects.reduce((sum, p) => sum + p.overdueTasks, 0);
  const totalTeamMembers = projects.reduce((sum, p) => sum + p.teamSize, 0);

  // Generate time series data for charts
  const last30Days = Array.from({ length: 30 }, (_, i) => {
    const date = addDays(new Date(), -29 + i);
    return {
      date: format(date, "MMM dd"),
      budget: Math.floor(Math.random() * 200000) + 100000,
      spent: Math.floor(Math.random() * 180000) + 80000,
      tasks: Math.floor(Math.random() * 10) + 5,
      completed: Math.floor(Math.random() * 8) + 2,
    };
  });

  // Resource utilization data
  const resourceData = [
    { type: "Labor", utilized: 85, available: 15, total: 180 },
    { type: "Equipment", utilized: 72, available: 28, total: 45 },
    { type: "Materials", utilized: 68, available: 32, total: 100 },
    { type: "Subcontractors", utilized: 90, available: 10, total: 25 },
  ];

  // Phase distribution
  const phaseData = [
    { phase: "Planning", value: 5, color: "#8b5cf6" },
    { phase: "Foundation", value: 15, color: "#3b82f6" },
    { phase: "Structure", value: 35, color: "#10b981" },
    { phase: "MEP", value: 25, color: "#f59e0b" },
    { phase: "Finishing", value: 15, color: "#ef4444" },
    { phase: "Closeout", value: 5, color: "#6b7280" },
  ];

  return {
    projects,
    metrics: {
      totalBudget,
      totalSpent,
      budgetUtilization: (totalSpent / totalBudget) * 100,
      totalTasks,
      completedTasks,
      taskCompletionRate: (completedTasks / totalTasks) * 100,
      overdueTasks,
      totalTeamMembers,
      avgProjectHealth:
        projects.reduce((sum, p) => sum + p.healthScore, 0) / projects.length,
      activeProjects: projects.filter((p) => p.status === "in_progress").length,
      highRiskProjects: projects.filter((p) => p.healthScore < 75).length,
    },
    timeSeriesData: last30Days,
    resourceData,
    phaseData,
  };
};

export function ProjectOverviewDashboard({
  userRole,
  userId,
  teamId,
  permissions,
}: ProjectOverviewDashboardProps) {
  const [timeRange, setTimeRange] = useState("30d");
  const [selectedMetric, setSelectedMetric] = useState("all");

  // Generate mock data
  const data = useMemo(() => generateMockData(), []);

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <Card className="p-3 shadow-lg">
          <p className="text-sm font-medium">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}:{" "}
              {entry.name.includes("$")
                ? formatAmount({ amount: entry.value, currency: "USD" })
                : entry.value}
            </p>
          ))}
        </Card>
      );
    }
    return null;
  };

  // Role-based metric cards
  const getMetricCards = () => {
    const baseCards = [
      {
        id: "projects",
        title: "Active Projects",
        value: data.metrics.activeProjects,
        change: 2,
        trend: "up",
        icon: Building2,
        color: "text-blue-600",
        bgColor: "bg-blue-100 dark:bg-blue-900/20",
        subtext: `${data.projects.length} total projects`,
      },
      {
        id: "tasks",
        title: "Task Completion",
        value: `${data.metrics.taskCompletionRate.toFixed(1)}%`,
        change: 5.2,
        trend: "up",
        icon: CheckCircle2,
        color: "text-green-600",
        bgColor: "bg-green-100 dark:bg-green-900/20",
        subtext: `${data.metrics.completedTasks} of ${data.metrics.totalTasks} tasks`,
        progress: data.metrics.taskCompletionRate,
      },
      {
        id: "health",
        title: "Project Health",
        value: `${data.metrics.avgProjectHealth.toFixed(0)}%`,
        change: -2.1,
        trend: "down",
        icon: Activity,
        color: "text-purple-600",
        bgColor: "bg-purple-100 dark:bg-purple-900/20",
        subtext:
          data.metrics.highRiskProjects > 0
            ? `${data.metrics.highRiskProjects} at risk`
            : "All healthy",
        status:
          data.metrics.avgProjectHealth >= 80
            ? "success"
            : data.metrics.avgProjectHealth >= 60
              ? "warning"
              : "error",
      },
    ];

    if (permissions.canViewFinancials) {
      baseCards.splice(1, 0, {
        id: "budget",
        title: "Budget Utilization",
        value: formatAmount({
          amount: data.metrics.totalSpent,
          currency: "USD",
        }),
        change: 8.3,
        trend: "up",
        icon: DollarSign,
        color: "text-emerald-600",
        bgColor: "bg-emerald-100 dark:bg-emerald-900/20",
        subtext: `${data.metrics.budgetUtilization.toFixed(1)}% of ${formatAmount({ amount: data.metrics.totalBudget, currency: "USD" })}`,
        progress: data.metrics.budgetUtilization,
      });
    }

    if (permissions.canManageTeam) {
      baseCards.push({
        id: "team",
        title: "Team Members",
        value: data.metrics.totalTeamMembers,
        change: 12,
        trend: "up",
        icon: Users,
        color: "text-orange-600",
        bgColor: "bg-orange-100 dark:bg-orange-900/20",
        subtext: "Across all projects",
      });
    }

    return baseCards;
  };

  const metricCards = getMetricCards();

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">Project Overview</h2>
          <p className="text-muted-foreground">
            Real-time insights across your construction portfolio
          </p>
        </div>

        <div className="flex items-center gap-3">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" size="sm">
            <Calendar className="mr-2 h-4 w-4" />
            Custom Range
          </Button>
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {metricCards.map((metric, index) => (
          <motion.div
            key={metric.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {metric.title}
                </CardTitle>
                <div className={cn("p-2 rounded-lg", metric.bgColor)}>
                  <metric.icon className={cn("h-4 w-4", metric.color)} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metric.value}</div>

                {metric.progress !== undefined && (
                  <Progress value={metric.progress} className="mt-2 h-1" />
                )}

                <div className="flex items-center justify-between mt-2">
                  <p className="text-xs text-muted-foreground">
                    {metric.subtext}
                  </p>

                  {metric.change !== undefined && (
                    <div
                      className={cn(
                        "flex items-center text-xs font-medium",
                        metric.trend === "up"
                          ? "text-green-600"
                          : "text-red-600",
                      )}
                    >
                      {metric.trend === "up" ? (
                        <ArrowUpRight className="mr-1 h-3 w-3" />
                      ) : (
                        <ArrowDownRight className="mr-1 h-3 w-3" />
                      )}
                      {Math.abs(metric.change)}%
                    </div>
                  )}
                </div>

                {metric.status && (
                  <div
                    className={cn(
                      "absolute top-0 right-0 w-1 h-full",
                      metric.status === "success"
                        ? "bg-green-500"
                        : metric.status === "warning"
                          ? "bg-yellow-500"
                          : "bg-red-500",
                    )}
                  />
                )}
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Project Status & Progress */}
        <div className="lg:col-span-2 space-y-6">
          {/* Active Projects */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Active Projects</CardTitle>
                <Button variant="ghost" size="sm">
                  View All
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.projects.map((project, index) => (
                  <motion.div
                    key={project.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <div className="flex items-start justify-between p-4 rounded-lg border bg-card hover:shadow-md transition-shadow">
                      <div className="flex-1 space-y-3">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold flex items-center gap-2">
                              {project.name}
                              {project.weatherRisk === "high" && (
                                <Badge
                                  variant="destructive"
                                  className="text-xs"
                                >
                                  <AlertTriangle className="mr-1 h-3 w-3" />
                                  Weather Risk
                                </Badge>
                              )}
                            </h4>
                            <div className="flex items-center gap-4 mt-1 text-sm text-muted-foreground">
                              <span className="flex items-center gap-1">
                                <MapPin className="h-3 w-3" />
                                {project.location}
                              </span>
                              <span className="flex items-center gap-1">
                                <Users className="h-3 w-3" />
                                {project.teamSize} members
                              </span>
                              <Badge variant="secondary" className="text-xs">
                                {project.activePhase}
                              </Badge>
                            </div>
                          </div>

                          <div className="text-right">
                            <div
                              className={cn(
                                "text-2xl font-bold",
                                project.healthScore >= 80
                                  ? "text-green-600"
                                  : project.healthScore >= 60
                                    ? "text-yellow-600"
                                    : "text-red-600",
                              )}
                            >
                              {project.healthScore}%
                            </div>
                            <p className="text-xs text-muted-foreground">
                              Health Score
                            </p>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-muted-foreground">
                              Overall Progress
                            </span>
                            <span className="font-medium">
                              {project.completionPercentage}%
                            </span>
                          </div>
                          <Progress
                            value={project.completionPercentage}
                            className="h-2"
                          />
                        </div>

                        <div className="grid grid-cols-3 gap-4 pt-2">
                          {permissions.canViewFinancials && (
                            <div>
                              <p className="text-xs text-muted-foreground">
                                Budget Used
                              </p>
                              <p className="text-sm font-medium">
                                {(
                                  (project.actualCost / project.budget) *
                                  100
                                ).toFixed(1)}
                                %
                              </p>
                            </div>
                          )}
                          <div>
                            <p className="text-xs text-muted-foreground">
                              Tasks
                            </p>
                            <p className="text-sm font-medium">
                              {project.completedTasks}/{project.totalTasks}
                              {project.overdueTasks > 0 && (
                                <span className="text-red-600 ml-1">
                                  ({project.overdueTasks} overdue)
                                </span>
                              )}
                            </p>
                          </div>
                          <div>
                            <p className="text-xs text-muted-foreground">
                              Days Left
                            </p>
                            <p className="text-sm font-medium">
                              {differenceInDays(
                                project.plannedEndDate,
                                new Date(),
                              )}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center justify-between pt-2 border-t">
                          <div className="flex items-center gap-2">
                            <Avatar className="h-6 w-6">
                              <AvatarImage
                                src={project.projectManager.avatar}
                              />
                              <AvatarFallback>
                                {project.projectManager.name.charAt(0)}
                              </AvatarFallback>
                            </Avatar>
                            <span className="text-sm text-muted-foreground">
                              {project.projectManager.name}
                            </span>
                          </div>

                          <div className="flex items-center gap-2">
                            <Badge
                              variant={
                                project.priority === "critical"
                                  ? "destructive"
                                  : project.priority === "high"
                                    ? "default"
                                    : "secondary"
                              }
                              className="text-xs"
                            >
                              {project.priority}
                            </Badge>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                            >
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Progress Chart */}
          {permissions.canAccessAnalytics && (
            <Card>
              <CardHeader>
                <CardTitle>Progress Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={data.timeSeriesData}>
                    <defs>
                      <linearGradient
                        id="colorTasks"
                        x1="0"
                        y1="0"
                        x2="0"
                        y2="1"
                      >
                        <stop
                          offset="5%"
                          stopColor="#3b82f6"
                          stopOpacity={0.8}
                        />
                        <stop
                          offset="95%"
                          stopColor="#3b82f6"
                          stopOpacity={0}
                        />
                      </linearGradient>
                      <linearGradient
                        id="colorCompleted"
                        x1="0"
                        y1="0"
                        x2="0"
                        y2="1"
                      >
                        <stop
                          offset="5%"
                          stopColor="#10b981"
                          stopOpacity={0.8}
                        />
                        <stop
                          offset="95%"
                          stopColor="#10b981"
                          stopOpacity={0}
                        />
                      </linearGradient>
                    </defs>
                    <XAxis dataKey="date" fontSize={12} />
                    <YAxis fontSize={12} />
                    <Tooltip content={<CustomTooltip />} />
                    <Area
                      type="monotone"
                      dataKey="tasks"
                      stroke="#3b82f6"
                      fillOpacity={1}
                      fill="url(#colorTasks)"
                      name="New Tasks"
                    />
                    <Area
                      type="monotone"
                      dataKey="completed"
                      stroke="#10b981"
                      fillOpacity={1}
                      fill="url(#colorCompleted)"
                      name="Completed"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right Column - Analytics & Insights */}
        <div className="space-y-6">
          {/* Resource Utilization */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Gauge className="h-5 w-5" />
                Resource Utilization
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.resourceData.map((resource) => (
                  <div key={resource.type} className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="font-medium">{resource.type}</span>
                      <span className="text-muted-foreground">
                        {resource.utilized}% utilized
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Progress value={resource.utilized} className="flex-1" />
                      <span className="text-xs text-muted-foreground w-12">
                        {resource.total}
                      </span>
                    </div>
                  </div>
                ))}
              </div>

              <Separator className="my-4" />

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  Overall Utilization
                </span>
                <span className="text-lg font-semibold">
                  {Math.round(
                    data.resourceData.reduce((sum, r) => sum + r.utilized, 0) /
                      data.resourceData.length,
                  )}
                  %
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Phase Distribution */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Project Phases
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={200}>
                <PieChart>
                  <Pie
                    data={data.phaseData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={80}
                    paddingAngle={2}
                    dataKey="value"
                  >
                    {data.phaseData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>

              <div className="grid grid-cols-2 gap-2 mt-4">
                {data.phaseData.map((phase) => (
                  <div key={phase.phase} className="flex items-center gap-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: phase.color }}
                    />
                    <span className="text-sm">
                      {phase.phase} ({phase.value}%)
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Insights */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5" />
                AI Insights
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-start gap-3 p-3 rounded-lg bg-blue-50 dark:bg-blue-900/20">
                  <Zap className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">
                      Efficiency Opportunity
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Consider reallocating 3 workers from Highway Bridge to
                      Riverside Complex for optimal progress.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3 p-3 rounded-lg bg-yellow-50 dark:bg-yellow-900/20">
                  <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">Weather Alert</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Rain forecasted for next week. Consider accelerating
                      outdoor work on 2 projects.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3 p-3 rounded-lg bg-green-50 dark:bg-green-900/20">
                  <TrendingUp className="h-5 w-5 text-green-600 mt-0.5" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">Ahead of Schedule</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Highway Bridge project is 5 days ahead. Consider early
                      resource reallocation.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
