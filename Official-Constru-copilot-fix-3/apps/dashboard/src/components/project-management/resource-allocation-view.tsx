"use client";

import { Badge } from "@constru/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@constru/ui/card";
import { Gauge, Package } from "lucide-react";

interface ResourceAllocationViewProps {
  userRole: string;
  teamId: string;
  permissions: any;
}

export function ResourceAllocationView({
  userRole,
  teamId,
  permissions,
}: ResourceAllocationViewProps) {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Gauge className="h-6 w-6 text-primary" />
          <div>
            <h2 className="text-2xl font-bold">Resource Allocation</h2>
            <p className="text-muted-foreground">
              Manage equipment, materials, and labor
            </p>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Resource Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <Package className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              Resource Allocation Coming Soon
            </h3>
            <p className="text-muted-foreground">
              Comprehensive resource tracking, allocation, and optimization
              tools.
            </p>
            <Badge variant="secondary" className="mt-4">
              In Development
            </Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
