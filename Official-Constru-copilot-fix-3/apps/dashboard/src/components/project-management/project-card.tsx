"use client";

import { formatAmount } from "@/utils/format";
import { Avatar, AvatarFallback, AvatarImage } from "@constru/ui/avatar";
import { Badge } from "@constru/ui/badge";
import { <PERSON><PERSON> } from "@constru/ui/button";
import { Card, CardContent, CardHeader } from "@constru/ui/card";
import { cn } from "@constru/ui/cn";
import { Progress } from "@constru/ui/progress";
import { Tooltip, TooltipContent, TooltipTrigger } from "@constru/ui/tooltip";
import { differenceInDays, format, isAfter, isBefore } from "date-fns";
import {
  AlertTriangle,
  Building2,
  Calendar,
  CheckCircle,
  Circle,
  Clock,
  DollarSign,
  ExternalLink,
  FileText,
  MapPin,
  MessageSquare,
  Minus,
  Settings,
  Timer,
  TrendingDown,
  TrendingUp,
  Users,
} from "lucide-react";
import Link from "next/link";

interface ProjectCardProps {
  project: {
    id: string;
    name: string;
    description?: string;
    status: string;
    projectType?: string;
    priority?: string;
    completionPercentage?: number;
    budget?: number;
    actualCost?: number;
    plannedStartDate?: string;
    plannedEndDate?: string;
    actualStartDate?: string;
    actualEndDate?: string;
    locationAddress?: string;
    totalTasks?: number;
    completedTasks?: number;
    inProgressTasks?: number;
    overdueTasks?: number;
    teamSize?: number;
    customer?: {
      id: string;
      name: string;
      email?: string;
    };
    projectManager?: {
      id: string;
      fullName: string;
      email?: string;
      avatarUrl?: string;
    };
    recentActivity?: string;
  };
  userRole?: string;
}

export function ProjectCard({ project, userRole }: ProjectCardProps) {
  const isProjectManager =
    userRole === "project_manager" || userRole === "admin";
  const canEdit = isProjectManager || userRole === "foreman";

  // Calculate project health indicators
  const budgetUtilization =
    project.budget && project.actualCost
      ? (project.actualCost / project.budget) * 100
      : 0;

  const isOverBudget = budgetUtilization > 100;
  const isBudgetWarning = budgetUtilization > 80;

  // Calculate timeline status
  const today = new Date();
  const plannedEnd = project.plannedEndDate
    ? new Date(project.plannedEndDate)
    : null;
  const isOverdue =
    plannedEnd && isAfter(today, plannedEnd) && project.status !== "completed";
  const daysRemaining = plannedEnd ? differenceInDays(plannedEnd, today) : null;

  // Project health score based on multiple factors
  const getProjectHealth = () => {
    let score = 100;

    if (isOverBudget) score -= 30;
    else if (isBudgetWarning) score -= 15;

    if (isOverdue) score -= 25;
    else if (daysRemaining && daysRemaining < 7) score -= 10;

    if (project.overdueTasks && project.overdueTasks > 0) {
      score -= Math.min(project.overdueTasks * 5, 20);
    }

    if (project.completionPercentage) {
      const expectedProgress = 50; // This could be calculated based on timeline
      if (project.completionPercentage < expectedProgress) score -= 15;
    }

    return Math.max(score, 0);
  };

  const healthScore = getProjectHealth();
  const healthColor =
    healthScore >= 80
      ? "text-green-600"
      : healthScore >= 60
        ? "text-yellow-600"
        : "text-red-600";

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 border-green-200";
      case "in_progress":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "on_hold":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "cancelled":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "critical":
        return "bg-red-500";
      case "high":
        return "bg-orange-500";
      case "medium":
        return "bg-yellow-500";
      case "low":
        return "bg-green-500";
      default:
        return "bg-gray-500";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "residential_construction":
        return <Building2 className="h-4 w-4" />;
      case "commercial_construction":
        return <Building2 className="h-4 w-4" />;
      case "renovation":
        return <Settings className="h-4 w-4" />;
      case "infrastructure":
        return <Building2 className="h-4 w-4" />;
      default:
        return <Building2 className="h-4 w-4" />;
    }
  };

  return (
    <Card className="group hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              {project.projectType && getTypeIcon(project.projectType)}
              <Link
                href={`/projects/${project.id}`}
                className="font-semibold text-lg hover:text-blue-600 transition-colors truncate"
              >
                {project.name}
              </Link>
            </div>
            {project.description && (
              <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
                {project.description}
              </p>
            )}
          </div>

          <div className="flex flex-col items-end gap-2 ml-3">
            <Badge className={getStatusColor(project.status)}>
              {project.status === "in_progress"
                ? "Active"
                : project.status === "completed"
                  ? "Completed"
                  : project.status.replace("_", " ")}
            </Badge>

            {project.priority && (
              <div className="flex items-center gap-1">
                <div
                  className={cn(
                    "w-2 h-2 rounded-full",
                    getPriorityColor(project.priority),
                  )}
                />
                <span className="text-xs text-muted-foreground capitalize">
                  {project.priority} Priority
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Customer and Location */}
        <div className="space-y-1">
          {project.customer && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Building2 className="h-3 w-3" />
              <span>{project.customer.name}</span>
            </div>
          )}

          {project.locationAddress && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <MapPin className="h-3 w-3" />
              <span className="truncate">{project.locationAddress}</span>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Progress Section */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Progress</span>
            <span className="font-medium">
              {project.completionPercentage || 0}%
            </span>
          </div>
          <Progress value={project.completionPercentage || 0} className="h-2" />
        </div>

        {/* Timeline Section */}
        {(project.plannedStartDate || project.plannedEndDate) && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Calendar className="h-3 w-3" />
              <span>Timeline</span>
            </div>
            <div className="grid grid-cols-2 gap-2 text-xs">
              {project.plannedStartDate && (
                <div>
                  <span className="text-muted-foreground block">Start:</span>
                  <span>
                    {format(new Date(project.plannedStartDate), "MMM dd")}
                  </span>
                </div>
              )}
              {project.plannedEndDate && (
                <div>
                  <span className="text-muted-foreground block">End:</span>
                  <span className={isOverdue ? "text-red-600 font-medium" : ""}>
                    {format(new Date(project.plannedEndDate), "MMM dd")}
                  </span>
                </div>
              )}
            </div>

            {daysRemaining !== null && (
              <div
                className={cn(
                  "text-xs p-2 rounded-md border",
                  isOverdue
                    ? "bg-red-50 border-red-200 text-red-700"
                    : daysRemaining < 7
                      ? "bg-yellow-50 border-yellow-200 text-yellow-700"
                      : "bg-green-50 border-green-200 text-green-700",
                )}
              >
                {isOverdue
                  ? `${Math.abs(daysRemaining)} days overdue`
                  : `${daysRemaining} days remaining`}
              </div>
            )}
          </div>
        )}

        {/* Financial Section */}
        {(project.budget || project.actualCost) && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <DollarSign className="h-3 w-3" />
              <span>Budget</span>
            </div>
            <div className="grid grid-cols-2 gap-2 text-xs">
              {project.budget && (
                <div>
                  <span className="text-muted-foreground block">Budget:</span>
                  <span>
                    {formatAmount({ amount: project.budget, currency: "USD" })}
                  </span>
                </div>
              )}
              {project.actualCost && (
                <div>
                  <span className="text-muted-foreground block">Spent:</span>
                  <span
                    className={isOverBudget ? "text-red-600 font-medium" : ""}
                  >
                    {formatAmount({
                      amount: project.actualCost,
                      currency: "USD",
                    })}
                  </span>
                </div>
              )}
            </div>

            {project.budget && project.actualCost && (
              <div className="space-y-1">
                <div className="flex items-center justify-between text-xs">
                  <span className="text-muted-foreground">Budget Usage</span>
                  <span
                    className={
                      budgetUtilization > 100 ? "text-red-600 font-medium" : ""
                    }
                  >
                    {budgetUtilization.toFixed(1)}%
                  </span>
                </div>
                <Progress
                  value={Math.min(budgetUtilization, 100)}
                  className="h-1"
                />
              </div>
            )}
          </div>
        )}

        {/* Tasks and Team Section */}
        <div className="grid grid-cols-2 gap-4">
          {/* Tasks */}
          {(project.totalTasks || 0) > 0 && (
            <div className="space-y-1">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <CheckCircle className="h-3 w-3" />
                <span>Tasks</span>
              </div>
              <div className="text-xs space-y-1">
                <div className="flex justify-between">
                  <span>Completed:</span>
                  <span className="font-medium">
                    {project.completedTasks || 0}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>In Progress:</span>
                  <span className="font-medium">
                    {project.inProgressTasks || 0}
                  </span>
                </div>
                {(project.overdueTasks || 0) > 0 && (
                  <div className="flex justify-between text-red-600">
                    <span>Overdue:</span>
                    <span className="font-medium">{project.overdueTasks}</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Team */}
          <div className="space-y-1">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Users className="h-3 w-3" />
              <span>Team</span>
            </div>
            <div className="flex items-center gap-2">
              {project.projectManager && (
                <Tooltip>
                  <TooltipTrigger>
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={project.projectManager.avatarUrl} />
                      <AvatarFallback className="text-xs">
                        {project.projectManager.fullName
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{project.projectManager.fullName} (Project Manager)</p>
                  </TooltipContent>
                </Tooltip>
              )}

              {(project.teamSize || 0) > 1 && (
                <span className="text-xs text-muted-foreground">
                  +{(project.teamSize || 1) - 1} more
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Health Score */}
        <div className="flex items-center justify-between p-2 bg-muted rounded-md">
          <span className="text-sm text-muted-foreground">Project Health</span>
          <div className="flex items-center gap-2">
            <div
              className={cn(
                "w-2 h-2 rounded-full",
                healthColor.replace("text-", "bg-"),
              )}
            />
            <span className={cn("text-sm font-medium", healthColor)}>
              {healthScore}%
            </span>
          </div>
        </div>

        {/* Recent Activity */}
        {project.recentActivity && (
          <div className="text-xs text-muted-foreground p-2 bg-muted rounded-md">
            <span className="font-medium">Latest: </span>
            <span className="line-clamp-2">{project.recentActivity}</span>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center gap-2 pt-2 border-t">
          <Button asChild size="sm" className="flex-1">
            <Link href={`/projects/${project.id}`}>
              <ExternalLink className="mr-2 h-3 w-3" />
              View Project
            </Link>
          </Button>

          {canEdit && (
            <Button variant="outline" size="sm">
              <Settings className="h-3 w-3" />
            </Button>
          )}

          <Button variant="outline" size="sm">
            <MessageSquare className="h-3 w-3" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
