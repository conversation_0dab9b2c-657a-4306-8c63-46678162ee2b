"use client";

import { Badge } from "@constru/ui/badge";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@constru/ui/card";
import { BarChart3, TrendingUp } from "lucide-react";

interface ProjectAnalyticsProps {
  userRole: string;
  teamId: string;
  permissions: any;
}

export function ProjectAnalytics({
  userRole,
  teamId,
  permissions,
}: ProjectAnalyticsProps) {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <BarChart3 className="h-6 w-6 text-primary" />
          <div>
            <h2 className="text-2xl font-bold">Project Analytics</h2>
            <p className="text-muted-foreground">
              Advanced insights and reporting
            </p>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Analytics Dashboard</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <TrendingUp className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              Advanced Analytics Coming Soon
            </h3>
            <p className="text-muted-foreground">
              Detailed project performance metrics, trend analysis, and
              predictive insights.
            </p>
            <Badge variant="secondary" className="mt-4">
              In Development
            </Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
