"use client";

import { Badge } from "@constru/ui/badge";
import { Button } from "@constru/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@constru/ui/card";
import { cn } from "@constru/ui/cn";
import {
  addMonths,
  eachDayOfInterval,
  endOfDay,
  endOfMonth,
  format,
  isSameDay,
  isSameMonth,
  isToday,
  isWithinInterval,
  parseISO,
  startOfDay,
  startOfMonth,
  subMonths,
} from "date-fns";
import {
  Building2,
  Calendar as CalendarIcon,
  ChevronLeft,
  ChevronRight,
  Clock,
  Users,
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

interface ProjectCalendarViewProps {
  projects: Array<{
    id: string;
    name: string;
    status: string;
    priority?: string;
    plannedStartDate?: string;
    plannedEndDate?: string;
    actualStartDate?: string;
    actualEndDate?: string;
    customer?: {
      name: string;
    };
    teamSize?: number;
  }>;
  userRole?: string;
}

export function ProjectCalendarView({
  projects,
  userRole,
}: ProjectCalendarViewProps) {
  const [currentMonth, setCurrentMonth] = useState(new Date());

  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(currentMonth);
  const calendarDays = eachDayOfInterval({ start: monthStart, end: monthEnd });

  const nextMonth = () => setCurrentMonth(addMonths(currentMonth, 1));
  const prevMonth = () => setCurrentMonth(subMonths(currentMonth, 1));
  const goToToday = () => setCurrentMonth(new Date());

  const getProjectsForDate = (date: Date) => {
    return projects.filter((project) => {
      const startDate = project.actualStartDate || project.plannedStartDate;
      const endDate = project.actualEndDate || project.plannedEndDate;

      if (!startDate || !endDate) return false;

      try {
        const projectStart = startOfDay(parseISO(startDate));
        const projectEnd = endOfDay(parseISO(endDate));

        return isWithinInterval(date, { start: projectStart, end: projectEnd });
      } catch {
        return false;
      }
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 border-green-200";
      case "in_progress":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "on_hold":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "cancelled":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "critical":
        return "border-l-red-500";
      case "high":
        return "border-l-orange-500";
      case "medium":
        return "border-l-yellow-500";
      case "low":
        return "border-l-green-500";
      default:
        return "border-l-gray-300";
    }
  };

  return (
    <div className="space-y-6">
      {/* Calendar Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <CardTitle className="flex items-center gap-2">
                <CalendarIcon className="h-5 w-5" />
                Project Timeline
              </CardTitle>
              <div className="text-2xl font-semibold">
                {format(currentMonth, "MMMM yyyy")}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={goToToday}>
                Today
              </Button>
              <Button variant="outline" size="sm" onClick={prevMonth}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={nextMonth}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Calendar Grid */}
      <Card>
        <CardContent className="p-6">
          {/* Days of Week Header */}
          <div className="grid grid-cols-7 gap-px mb-4">
            {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
              <div
                key={day}
                className="p-2 text-center text-sm font-medium text-muted-foreground bg-muted"
              >
                {day}
              </div>
            ))}
          </div>

          {/* Calendar Days */}
          <div className="grid grid-cols-7 gap-px bg-muted">
            {calendarDays.map((day) => {
              const dayProjects = getProjectsForDate(day);
              const isCurrentMonth = isSameMonth(day, currentMonth);
              const isCurrentDay = isToday(day);

              return (
                <div
                  key={day.toISOString()}
                  className={cn(
                    "min-h-[120px] bg-background p-2 border",
                    !isCurrentMonth && "opacity-30",
                    isCurrentDay && "bg-blue-50 border-blue-200",
                  )}
                >
                  {/* Day Number */}
                  <div
                    className={cn(
                      "text-sm font-medium mb-2",
                      isCurrentDay && "text-blue-600",
                    )}
                  >
                    {format(day, "d")}
                  </div>

                  {/* Projects for this day */}
                  <div className="space-y-1">
                    {dayProjects.slice(0, 3).map((project, index) => (
                      <Link
                        key={`${project.id}-${index}`}
                        href={`/projects/${project.id}`}
                        className={cn(
                          "block p-1 rounded text-xs border-l-2 hover:shadow-sm transition-shadow",
                          getPriorityColor(project.priority || "medium"),
                          getStatusColor(project.status),
                        )}
                      >
                        <div className="font-medium truncate">
                          {project.name}
                        </div>
                        {project.customer && (
                          <div className="text-muted-foreground truncate">
                            {project.customer.name}
                          </div>
                        )}
                      </Link>
                    ))}

                    {dayProjects.length > 3 && (
                      <div className="text-xs text-muted-foreground text-center p-1">
                        +{dayProjects.length - 3} more
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Projects List for Current Month */}
      <Card>
        <CardHeader>
          <CardTitle>Projects in {format(currentMonth, "MMMM yyyy")}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {projects
              .filter((project) => {
                const startDate =
                  project.actualStartDate || project.plannedStartDate;
                const endDate = project.actualEndDate || project.plannedEndDate;

                if (!startDate || !endDate) return false;

                try {
                  const projectStart = parseISO(startDate);
                  const projectEnd = parseISO(endDate);

                  return (
                    isWithinInterval(monthStart, {
                      start: projectStart,
                      end: projectEnd,
                    }) ||
                    isWithinInterval(monthEnd, {
                      start: projectStart,
                      end: projectEnd,
                    }) ||
                    isWithinInterval(projectStart, {
                      start: monthStart,
                      end: monthEnd,
                    }) ||
                    isWithinInterval(projectEnd, {
                      start: monthStart,
                      end: monthEnd,
                    })
                  );
                } catch {
                  return false;
                }
              })
              .map((project) => {
                const startDate =
                  project.actualStartDate || project.plannedStartDate;
                const endDate = project.actualEndDate || project.plannedEndDate;

                return (
                  <div
                    key={project.id}
                    className={cn(
                      "flex items-center justify-between p-3 border rounded-lg border-l-4",
                      getPriorityColor(project.priority || "medium"),
                    )}
                  >
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-blue-50 rounded">
                        <Building2 className="h-4 w-4 text-blue-600" />
                      </div>

                      <div>
                        <Link
                          href={`/projects/${project.id}`}
                          className="font-medium hover:text-blue-600 transition-colors"
                        >
                          {project.name}
                        </Link>
                        {project.customer && (
                          <div className="text-sm text-muted-foreground">
                            {project.customer.name}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center gap-4 text-sm">
                      <div className="flex items-center gap-1 text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        <span>
                          {startDate && format(parseISO(startDate), "MMM dd")} -{" "}
                          {endDate && format(parseISO(endDate), "MMM dd")}
                        </span>
                      </div>

                      {project.teamSize && (
                        <div className="flex items-center gap-1 text-muted-foreground">
                          <Users className="h-3 w-3" />
                          <span>{project.teamSize}</span>
                        </div>
                      )}

                      <Badge className={getStatusColor(project.status)}>
                        {project.status === "in_progress"
                          ? "Active"
                          : project.status === "completed"
                            ? "Completed"
                            : project.status.replace("_", " ")}
                      </Badge>
                    </div>
                  </div>
                );
              })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
