"use client";

import { formatAmount } from "@/utils/format";
import { Avatar, AvatarFallback, AvatarImage } from "@constru/ui/avatar";
import { Badge } from "@constru/ui/badge";
import { Button } from "@constru/ui/button";
import { Card } from "@constru/ui/card";
import { cn } from "@constru/ui/cn";
import { Progress } from "@constru/ui/progress";
import { format } from "date-fns";
import {
  Building2,
  Calendar,
  DollarSign,
  ExternalLink,
  MapPin,
  MessageSquare,
  Settings,
  Users,
} from "lucide-react";
import Link from "next/link";

interface ProjectListViewProps {
  projects: Array<{
    id: string;
    name: string;
    description?: string;
    status: string;
    projectType?: string;
    priority?: string;
    completionPercentage?: number;
    budget?: number;
    actualCost?: number;
    plannedEndDate?: string;
    locationAddress?: string;
    totalTasks?: number;
    completedTasks?: number;
    teamSize?: number;
    customer?: {
      name: string;
    };
    projectManager?: {
      fullName: string;
      avatarUrl?: string;
    };
  }>;
  userRole?: string;
}

export function ProjectListView({ projects, userRole }: ProjectListViewProps) {
  const canEdit =
    userRole === "project_manager" ||
    userRole === "admin" ||
    userRole === "foreman";

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 border-green-200";
      case "in_progress":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "on_hold":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "cancelled":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "critical":
        return "bg-red-500";
      case "high":
        return "bg-orange-500";
      case "medium":
        return "bg-yellow-500";
      case "low":
        return "bg-green-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <Card>
      <div className="divide-y">
        {projects.map((project) => (
          <div
            key={project.id}
            className="p-6 hover:bg-muted/50 transition-colors"
          >
            <div className="flex items-start gap-4">
              {/* Project Icon */}
              <div className="p-2 bg-blue-50 rounded-md">
                <Building2 className="h-5 w-5 text-blue-600" />
              </div>

              {/* Main Content */}
              <div className="flex-1 min-w-0 space-y-3">
                {/* Header */}
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <div className="flex items-center gap-3">
                      <Link
                        href={`/projects/${project.id}`}
                        className="font-semibold text-lg hover:text-blue-600 transition-colors"
                      >
                        {project.name}
                      </Link>

                      <Badge className={getStatusColor(project.status)}>
                        {project.status === "in_progress"
                          ? "Active"
                          : project.status === "completed"
                            ? "Completed"
                            : project.status.replace("_", " ")}
                      </Badge>

                      {project.priority && (
                        <div className="flex items-center gap-1">
                          <div
                            className={cn(
                              "w-2 h-2 rounded-full",
                              getPriorityColor(project.priority),
                            )}
                          />
                          <span className="text-xs text-muted-foreground capitalize">
                            {project.priority}
                          </span>
                        </div>
                      )}
                    </div>

                    {project.description && (
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {project.description}
                      </p>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-2">
                    <Button asChild size="sm" variant="outline">
                      <Link href={`/projects/${project.id}`}>
                        <ExternalLink className="mr-2 h-3 w-3" />
                        View
                      </Link>
                    </Button>

                    {canEdit && (
                      <>
                        <Button size="sm" variant="outline">
                          <Settings className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <MessageSquare className="h-3 w-3" />
                        </Button>
                      </>
                    )}
                  </div>
                </div>

                {/* Metadata Row */}
                <div className="flex items-center gap-6 text-sm text-muted-foreground">
                  {project.customer && (
                    <div className="flex items-center gap-1">
                      <Building2 className="h-3 w-3" />
                      <span>{project.customer.name}</span>
                    </div>
                  )}

                  {project.locationAddress && (
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      <span className="truncate max-w-48">
                        {project.locationAddress}
                      </span>
                    </div>
                  )}

                  {project.plannedEndDate && (
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      <span>
                        Due{" "}
                        {format(
                          new Date(project.plannedEndDate),
                          "MMM dd, yyyy",
                        )}
                      </span>
                    </div>
                  )}
                </div>

                {/* Progress and Stats */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  {/* Progress */}
                  <div className="space-y-1">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-muted-foreground">Progress</span>
                      <span className="font-medium">
                        {project.completionPercentage || 0}%
                      </span>
                    </div>
                    <Progress
                      value={project.completionPercentage || 0}
                      className="h-2"
                    />
                  </div>

                  {/* Tasks */}
                  {(project.totalTasks || 0) > 0 && (
                    <div className="text-center">
                      <div className="text-lg font-semibold">
                        {project.completedTasks || 0}/{project.totalTasks || 0}
                      </div>
                      <div className="text-xs text-muted-foreground">Tasks</div>
                    </div>
                  )}

                  {/* Budget */}
                  {(project.budget || project.actualCost) && (
                    <div className="text-center">
                      <div className="text-lg font-semibold">
                        {formatAmount({
                          amount: project.actualCost || 0,
                          currency: "USD",
                        })}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        of{" "}
                        {formatAmount({
                          amount: project.budget || 0,
                          currency: "USD",
                        })}
                      </div>
                    </div>
                  )}

                  {/* Team */}
                  <div className="flex items-center gap-2">
                    {project.projectManager && (
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={project.projectManager.avatarUrl} />
                        <AvatarFallback className="text-xs">
                          {project.projectManager.fullName
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                    )}

                    <div className="text-xs">
                      <div className="font-medium">
                        {project.projectManager?.fullName}
                      </div>
                      <div className="text-muted-foreground">
                        {(project.teamSize || 0) > 1 &&
                          `+${(project.teamSize || 1) - 1} team members`}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
}
