"use client";

import { useTR<PERSON> } from "@/trpc/client";
import { formatAmount } from "@/utils/format";
import { Badge } from "@constru/ui/badge";
import { But<PERSON> } from "@constru/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@constru/ui/card";
import { cn } from "@constru/ui/cn";
import { Input } from "@constru/ui/input";
import { Progress } from "@constru/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@constru/ui/select";
import { Separator } from "@constru/ui/separator";
import { Skeleton } from "@constru/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@constru/ui/tabs";
import { differenceInDays } from "date-fns";
import {
  Activity,
  AlertTriangle,
  BarChart3,
  Briefcase,
  Building2,
  Calendar,
  CheckCircle,
  Clock,
  Crown,
  DollarSign,
  Download,
  Eye,
  Filter,
  Grid3X3,
  Hammer,
  Home,
  Kanban,
  List,
  Plus,
  Search,
  Settings,
  Shield,
  Target,
  TrendingDown,
  TrendingUp,
  Upload,
  User,
  Users,
} from "lucide-react";
import Link from "next/link";
import { useMemo, useState } from "react";
import { ProjectCalendarView } from "./project-calendar-view";
import { ProjectCard } from "./project-card";
import { ProjectKanbanView } from "./project-kanban-view";
import { ProjectListView } from "./project-list-view";
import { ProjectStats } from "./project-stats";

type ViewMode = "grid" | "list" | "kanban" | "calendar";

interface EnhancedProjectDashboardProps {
  userRole?:
    | "project_manager"
    | "foreman"
    | "worker"
    | "client"
    | "supervisor"
    | "admin"
    | "subcontractor";
  userId: string;
  teamId: string;
}

export function EnhancedProjectDashboard({
  userRole = "worker",
  userId,
  teamId,
}: EnhancedProjectDashboardProps) {
  const trpc = useTRPC();
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [projectTypeFilter, setProjectTypeFilter] = useState<string>("all");
  const [priorityFilter, setPriorityFilter] = useState<string>("all");
  const [timeRangeFilter, setTimeRangeFilter] = useState<string>("all");

  // Fetch projects based on user role
  const {
    data: projects,
    isLoading,
    error,
  } = trpc.projectManagement.dashboard.useQuery({
    projectIds: undefined, // Optional: specify specific project IDs
    status: statusFilter !== "all" ? statusFilter : undefined,
    limit: 100,
  });

  // Fetch team data for management overview
  const { data: teamOverview } = trpc.team.get.useQuery();

  // Filter projects based on search and filters
  const filteredProjects = useMemo(() => {
    if (!projects) return [];

    return projects.filter((project) => {
      const matchesSearch =
        !searchQuery ||
        project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        project.description
          ?.toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        project.customer?.name
          .toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        project.locationAddress
          ?.toLowerCase()
          .includes(searchQuery.toLowerCase());

      const matchesType =
        projectTypeFilter === "all" ||
        project.projectType === projectTypeFilter;
      const matchesPriority =
        priorityFilter === "all" || project.priority === priorityFilter;

      // Time range filtering
      let matchesTimeRange = true;
      if (timeRangeFilter !== "all" && project.plannedStartDate) {
        const projectDate = new Date(project.plannedStartDate);
        const now = new Date();
        const daysAgo = differenceInDays(now, projectDate);

        switch (timeRangeFilter) {
          case "week":
            matchesTimeRange = daysAgo <= 7;
            break;
          case "month":
            matchesTimeRange = daysAgo <= 30;
            break;
          case "quarter":
            matchesTimeRange = daysAgo <= 90;
            break;
          case "year":
            matchesTimeRange = daysAgo <= 365;
            break;
        }
      }

      return (
        matchesSearch && matchesType && matchesPriority && matchesTimeRange
      );
    });
  }, [
    projects,
    searchQuery,
    projectTypeFilter,
    priorityFilter,
    timeRangeFilter,
  ]);

  // Calculate comprehensive dashboard metrics
  const dashboardMetrics = useMemo(() => {
    if (!projects) return null;

    const totalProjects = projects.length;
    const activeProjects = projects.filter(
      (p) => p.status === "in_progress",
    ).length;
    const completedProjects = projects.filter(
      (p) => p.status === "completed",
    ).length;
    const onHoldProjects = projects.filter(
      (p) => p.status === "on_hold",
    ).length;
    const overdueProjects = projects.filter((p) => {
      if (!p.plannedEndDate || p.status === "completed") return false;
      return new Date() > new Date(p.plannedEndDate);
    }).length;

    const totalBudget = projects.reduce((sum, p) => sum + (p.budget || 0), 0);
    const totalSpent = projects.reduce(
      (sum, p) => sum + (p.actualCost || 0),
      0,
    );
    const budgetUtilization =
      totalBudget > 0 ? (totalSpent / totalBudget) * 100 : 0;
    const remainingBudget = totalBudget - totalSpent;

    const totalTasks = projects.reduce(
      (sum, p) => sum + (p.totalTasks || 0),
      0,
    );
    const completedTasks = projects.reduce(
      (sum, p) => sum + (p.completedTasks || 0),
      0,
    );
    const inProgressTasks = projects.reduce(
      (sum, p) => sum + (p.inProgressTasks || 0),
      0,
    );
    const overdueTasks = projects.reduce(
      (sum, p) => sum + (p.overdueTasks || 0),
      0,
    );
    const taskCompletionRate =
      totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

    const totalTeamMembers = projects.reduce(
      (sum, p) => sum + (p.teamSize || 0),
      0,
    );
    const avgTeamSize =
      totalProjects > 0 ? totalTeamMembers / totalProjects : 0;

    // Project health scoring
    const projectHealthScores = projects.map((project) => {
      let health = 100;

      // Budget health
      if (project.budget && project.actualCost) {
        const budgetUsage = (project.actualCost / project.budget) * 100;
        if (budgetUsage > 100) health -= 30;
        else if (budgetUsage > 80) health -= 15;
      }

      // Timeline health
      if (project.plannedEndDate) {
        const daysOverdue = differenceInDays(
          new Date(),
          new Date(project.plannedEndDate),
        );
        if (daysOverdue > 0 && project.status !== "completed") health -= 25;
        else if (daysOverdue > -7) health -= 10; // Due soon
      }

      // Task health
      if (project.overdueTasks && project.overdueTasks > 0) {
        health -= Math.min(project.overdueTasks * 5, 20);
      }

      // Progress health
      if (project.plannedStartDate && project.plannedEndDate) {
        const totalDays = differenceInDays(
          new Date(project.plannedEndDate),
          new Date(project.plannedStartDate),
        );
        const elapsedDays = differenceInDays(
          new Date(),
          new Date(project.plannedStartDate),
        );
        const expectedProgress = Math.max(
          0,
          Math.min(100, (elapsedDays / totalDays) * 100),
        );
        const actualProgress = project.completionPercentage || 0;

        if (actualProgress < expectedProgress - 20) health -= 15;
      }

      return Math.max(0, health);
    });

    const avgProjectHealth =
      projectHealthScores.length > 0
        ? projectHealthScores.reduce((sum, score) => sum + score, 0) /
          projectHealthScores.length
        : 0;

    // Risk assessment
    const highRiskProjects = projects.filter((p) => {
      const isOverBudget =
        p.budget && p.actualCost && p.actualCost / p.budget > 1;
      const isOverdue =
        p.plannedEndDate &&
        new Date() > new Date(p.plannedEndDate) &&
        p.status !== "completed";
      const hasOverdueTasks = (p.overdueTasks || 0) > 0;
      return isOverBudget || isOverdue || hasOverdueTasks;
    }).length;

    return {
      overview: {
        totalProjects,
        activeProjects,
        completedProjects,
        onHoldProjects,
        overdueProjects,
        highRiskProjects,
      },
      financial: {
        totalBudget,
        totalSpent,
        remainingBudget,
        budgetUtilization,
        avgProjectBudget: totalProjects > 0 ? totalBudget / totalProjects : 0,
      },
      tasks: {
        totalTasks,
        completedTasks,
        inProgressTasks,
        overdueTasks,
        taskCompletionRate,
        avgTasksPerProject: totalProjects > 0 ? totalTasks / totalProjects : 0,
      },
      team: {
        totalTeamMembers,
        avgTeamSize,
        utilization: 85, // This would be calculated from actual time tracking data
      },
      health: {
        avgProjectHealth,
        status:
          avgProjectHealth >= 80
            ? "excellent"
            : avgProjectHealth >= 60
              ? "good"
              : avgProjectHealth >= 40
                ? "warning"
                : "critical",
      },
    };
  }, [projects]);

  // Role-based permissions and customization
  const roleConfig = useMemo(() => {
    const configs = {
      admin: {
        canCreateProject: true,
        canViewAllProjects: true,
        canManageTeam: true,
        canViewFinancials: true,
        canExportData: true,
        canManageSettings: true,
        dashboardTitle: "Administrator Dashboard",
        roleIcon: Crown,
        roleColor: "text-purple-600",
        defaultView: "grid" as ViewMode,
      },
      project_manager: {
        canCreateProject: true,
        canViewAllProjects: true,
        canManageTeam: true,
        canViewFinancials: true,
        canExportData: true,
        canManageSettings: false,
        dashboardTitle: "Project Manager Dashboard",
        roleIcon: Briefcase,
        roleColor: "text-blue-600",
        defaultView: "kanban" as ViewMode,
      },
      supervisor: {
        canCreateProject: false,
        canViewAllProjects: true,
        canManageTeam: false,
        canViewFinancials: false,
        canExportData: true,
        canManageSettings: false,
        dashboardTitle: "Supervisor Dashboard",
        roleIcon: Shield,
        roleColor: "text-green-600",
        defaultView: "list" as ViewMode,
      },
      foreman: {
        canCreateProject: false,
        canViewAllProjects: false,
        canManageTeam: false,
        canViewFinancials: false,
        canExportData: false,
        canManageSettings: false,
        dashboardTitle: "Foreman Dashboard",
        roleIcon: Hammer,
        roleColor: "text-orange-600",
        defaultView: "calendar" as ViewMode,
      },
      subcontractor: {
        canCreateProject: false,
        canViewAllProjects: false,
        canManageTeam: false,
        canViewFinancials: false,
        canExportData: false,
        canManageSettings: false,
        dashboardTitle: "Subcontractor Portal",
        roleIcon: User,
        roleColor: "text-indigo-600",
        defaultView: "grid" as ViewMode,
      },
      worker: {
        canCreateProject: false,
        canViewAllProjects: false,
        canManageTeam: false,
        canViewFinancials: false,
        canExportData: false,
        canManageSettings: false,
        dashboardTitle: "Worker Dashboard",
        roleIcon: User,
        roleColor: "text-gray-600",
        defaultView: "list" as ViewMode,
      },
      client: {
        canCreateProject: false,
        canViewAllProjects: false,
        canManageTeam: false,
        canViewFinancials: true,
        canExportData: false,
        canManageSettings: false,
        dashboardTitle: "Client Portal",
        roleIcon: Home,
        roleColor: "text-emerald-600",
        defaultView: "grid" as ViewMode,
      },
    };
    return configs[userRole] || configs.worker;
  }, [userRole]);

  // Set default view mode based on role
  useState(() => {
    setViewMode(roleConfig.defaultView);
  });

  if (isLoading) {
    return <EnhancedProjectDashboardSkeleton userRole={userRole} />;
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-96">
        <Card className="p-8 text-center max-w-md">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-red-600 mb-2">
            Error Loading Projects
          </h3>
          <p className="text-muted-foreground mb-4">{error.message}</p>
          <Button onClick={() => window.location.reload()}>Try Again</Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Enhanced Header */}
      <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-2">
            <roleConfig.roleIcon
              className={cn("h-8 w-8", roleConfig.roleColor)}
            />
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                {roleConfig.dashboardTitle}
              </h1>
              <p className="text-lg text-muted-foreground">
                {filteredProjects.length} of {projects?.length || 0} projects
                {roleConfig.canViewAllProjects
                  ? " • Full Access"
                  : " • Assigned Projects"}
              </p>
            </div>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
          <div className="flex items-center gap-2">
            {roleConfig.canExportData && (
              <>
                <Button variant="outline" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
                <Button variant="outline" size="sm">
                  <Upload className="mr-2 h-4 w-4" />
                  Import
                </Button>
              </>
            )}
            {roleConfig.canManageSettings && (
              <Button variant="outline" size="sm">
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </Button>
            )}
          </div>

          {roleConfig.canCreateProject && (
            <Button size="sm" className="bg-primary hover:bg-primary/90">
              <Plus className="mr-2 h-4 w-4" />
              New Project
            </Button>
          )}
        </div>
      </div>

      {/* Enhanced Dashboard Metrics */}
      {dashboardMetrics && roleConfig.canViewAllProjects && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Projects Overview */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Projects</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {dashboardMetrics.overview.totalProjects}
              </div>
              <div className="flex items-center justify-between text-xs text-muted-foreground mt-2">
                <span className="text-green-600">
                  {dashboardMetrics.overview.activeProjects} active
                </span>
                <span className="text-blue-600">
                  {dashboardMetrics.overview.completedProjects} done
                </span>
                {dashboardMetrics.overview.overdueProjects > 0 && (
                  <span className="text-red-600">
                    {dashboardMetrics.overview.overdueProjects} overdue
                  </span>
                )}
              </div>
              <Progress
                value={
                  (dashboardMetrics.overview.completedProjects /
                    dashboardMetrics.overview.totalProjects) *
                  100
                }
                className="mt-2 h-1"
              />
            </CardContent>
          </Card>

          {/* Task Progress */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Task Progress
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {dashboardMetrics.tasks.taskCompletionRate.toFixed(1)}%
              </div>
              <Progress
                value={dashboardMetrics.tasks.taskCompletionRate}
                className="mt-2"
              />
              <div className="flex items-center justify-between text-xs text-muted-foreground mt-2">
                <span>{dashboardMetrics.tasks.completedTasks} completed</span>
                <span>{dashboardMetrics.tasks.inProgressTasks} active</span>
                {dashboardMetrics.tasks.overdueTasks > 0 && (
                  <span className="text-red-600">
                    {dashboardMetrics.tasks.overdueTasks} overdue
                  </span>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Budget Overview (if user has financial access) */}
          {roleConfig.canViewFinancials && (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Budget</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatAmount({
                    amount: dashboardMetrics.financial.remainingBudget,
                    currency: "USD",
                  })}
                </div>
                <Progress
                  value={Math.min(
                    dashboardMetrics.financial.budgetUtilization,
                    100,
                  )}
                  className="mt-2"
                />
                <div className="flex items-center justify-between text-xs text-muted-foreground mt-2">
                  <span>
                    {dashboardMetrics.financial.budgetUtilization.toFixed(1)}%
                    used
                  </span>
                  <span>
                    {formatAmount({
                      amount: dashboardMetrics.financial.totalBudget,
                      currency: "USD",
                    })}{" "}
                    total
                  </span>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Project Health */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Health Score
              </CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {dashboardMetrics.health.avgProjectHealth.toFixed(0)}%
              </div>
              <div
                className={cn(
                  "text-xs font-medium mt-1",
                  dashboardMetrics.health.status === "excellent"
                    ? "text-green-600"
                    : dashboardMetrics.health.status === "good"
                      ? "text-blue-600"
                      : dashboardMetrics.health.status === "warning"
                        ? "text-yellow-600"
                        : "text-red-600",
                )}
              >
                {dashboardMetrics.health.status === "excellent"
                  ? "Excellent"
                  : dashboardMetrics.health.status === "good"
                    ? "Good"
                    : dashboardMetrics.health.status === "warning"
                      ? "Needs Attention"
                      : "Critical"}
              </div>
              <div className="flex items-center justify-between text-xs text-muted-foreground mt-2">
                <span>
                  {dashboardMetrics.team.totalTeamMembers} team members
                </span>
                {dashboardMetrics.overview.highRiskProjects > 0 && (
                  <span className="text-red-600">
                    {dashboardMetrics.overview.highRiskProjects} at risk
                  </span>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Enhanced Search and Filters */}
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search projects, customers, locations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="flex flex-col sm:flex-row gap-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full sm:w-[140px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="in_progress">Active</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="on_hold">On Hold</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={projectTypeFilter}
            onValueChange={setProjectTypeFilter}
          >
            <SelectTrigger className="w-full sm:w-[160px]">
              <SelectValue placeholder="Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="residential_construction">
                Residential
              </SelectItem>
              <SelectItem value="commercial_construction">
                Commercial
              </SelectItem>
              <SelectItem value="renovation">Renovation</SelectItem>
              <SelectItem value="infrastructure">Infrastructure</SelectItem>
              <SelectItem value="maintenance">Maintenance</SelectItem>
              <SelectItem value="custom">Custom</SelectItem>
            </SelectContent>
          </Select>

          <Select value={priorityFilter} onValueChange={setPriorityFilter}>
            <SelectTrigger className="w-full sm:w-[140px]">
              <SelectValue placeholder="Priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Priority</SelectItem>
              <SelectItem value="critical">Critical</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="low">Low</SelectItem>
            </SelectContent>
          </Select>

          <Select value={timeRangeFilter} onValueChange={setTimeRangeFilter}>
            <SelectTrigger className="w-full sm:w-[140px]">
              <SelectValue placeholder="Time Range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Time</SelectItem>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
              <SelectItem value="quarter">This Quarter</SelectItem>
              <SelectItem value="year">This Year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Enhanced View Toggle and Content */}
      <Tabs
        value={viewMode}
        onValueChange={(value) => setViewMode(value as ViewMode)}
        className="space-y-4"
      >
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <TabsList className="grid w-full sm:w-auto grid-cols-2 sm:grid-cols-4">
            <TabsTrigger value="grid" className="flex items-center gap-2">
              <Grid3X3 className="h-4 w-4" />
              <span className="hidden sm:inline">Grid</span>
            </TabsTrigger>
            <TabsTrigger value="list" className="flex items-center gap-2">
              <List className="h-4 w-4" />
              <span className="hidden sm:inline">List</span>
            </TabsTrigger>
            <TabsTrigger value="kanban" className="flex items-center gap-2">
              <Kanban className="h-4 w-4" />
              <span className="hidden sm:inline">Kanban</span>
            </TabsTrigger>
            <TabsTrigger value="calendar" className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <span className="hidden sm:inline">Calendar</span>
            </TabsTrigger>
          </TabsList>

          <div className="flex items-center gap-4">
            <div className="text-sm text-muted-foreground">
              Showing {filteredProjects.length} of {projects?.length || 0}{" "}
              projects
            </div>
            {filteredProjects.length !== projects?.length && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setSearchQuery("");
                  setStatusFilter("all");
                  setProjectTypeFilter("all");
                  setPriorityFilter("all");
                  setTimeRangeFilter("all");
                }}
              >
                Clear Filters
              </Button>
            )}
          </div>
        </div>

        <TabsContent value="grid">
          {filteredProjects && filteredProjects.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredProjects.map((project) => (
                <ProjectCard
                  key={project.id}
                  project={project}
                  userRole={userRole}
                />
              ))}
            </div>
          ) : (
            <EmptyProjectState
              hasFilters={
                searchQuery ||
                statusFilter !== "all" ||
                projectTypeFilter !== "all" ||
                priorityFilter !== "all" ||
                timeRangeFilter !== "all"
              }
              canCreateProject={roleConfig.canCreateProject}
              userRole={userRole}
            />
          )}
        </TabsContent>

        <TabsContent value="list">
          <ProjectListView
            projects={filteredProjects || []}
            userRole={userRole}
          />
        </TabsContent>

        <TabsContent value="kanban">
          <ProjectKanbanView
            projects={filteredProjects || []}
            userRole={userRole}
          />
        </TabsContent>

        <TabsContent value="calendar">
          <ProjectCalendarView
            projects={filteredProjects || []}
            userRole={userRole}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}

function EnhancedProjectDashboardSkeleton({ userRole }: { userRole: string }) {
  return (
    <div className="space-y-6">
      {/* Header Skeleton */}
      <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
        <div className="flex items-center gap-3">
          <Skeleton className="h-8 w-8 rounded-md" />
          <div>
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-5 w-40 mt-2" />
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Skeleton className="h-9 w-20" />
          <Skeleton className="h-9 w-20" />
          <Skeleton className="h-9 w-32" />
        </div>
      </div>

      {/* Metrics Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16" />
              <Skeleton className="h-2 w-full mt-2" />
              <div className="flex items-center justify-between mt-2">
                <Skeleton className="h-3 w-12" />
                <Skeleton className="h-3 w-12" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Filters Skeleton */}
      <div className="flex flex-col lg:flex-row gap-4">
        <Skeleton className="h-10 flex-1" />
        <div className="flex gap-2">
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-10 w-32" />
        </div>
      </div>

      {/* Content Skeleton */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-10 w-64" />
          <Skeleton className="h-6 w-32" />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, i) => (
            <Card key={i} className="p-6">
              <div className="space-y-4">
                <div className="flex items-start justify-between">
                  <Skeleton className="h-6 w-48" />
                  <Skeleton className="h-6 w-16" />
                </div>
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-2 w-full" />
                <div className="flex items-center gap-2">
                  <Skeleton className="h-6 w-16" />
                  <Skeleton className="h-6 w-20" />
                </div>
                <div className="flex items-center justify-between">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-16" />
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}

function EmptyProjectState({
  hasFilters,
  canCreateProject,
  userRole,
}: {
  hasFilters: boolean;
  canCreateProject: boolean;
  userRole: string;
}) {
  return (
    <Card className="p-12 text-center">
      <div className="mx-auto w-32 h-32 mb-6 opacity-20">
        <Building2 className="w-full h-full" />
      </div>
      <h3 className="text-lg font-semibold mb-2">
        {hasFilters ? "No projects match your filters" : "No projects found"}
      </h3>
      <p className="text-muted-foreground mb-6 max-w-md mx-auto">
        {hasFilters
          ? "Try adjusting your search criteria or filters to find the projects you're looking for."
          : canCreateProject
            ? "Get started by creating your first construction project to track progress, manage teams, and monitor budgets."
            : `Contact your project manager to get assigned to projects. As a ${userRole.replace("_", " ")}, you'll see projects once they're assigned to you.`}
      </p>
      {canCreateProject && (
        <Button size="lg">
          <Plus className="mr-2 h-5 w-5" />
          Create Your First Project
        </Button>
      )}
      {hasFilters && (
        <Button
          variant="outline"
          size="lg"
          className="ml-3"
          onClick={() => {
            // Clear filters would be handled by parent component
          }}
        >
          Clear All Filters
        </Button>
      )}
    </Card>
  );
}
