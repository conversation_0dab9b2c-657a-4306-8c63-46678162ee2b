"use client";

import { Badge } from "@constru/ui/badge";
import { Button } from "@constru/ui/button";
import { Card } from "@constru/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@constru/ui/select";
import {
  Building2,
  Calendar,
  DollarSign,
  Filter,
  MapPin,
  Users,
  X,
} from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";

export function ProjectFilters() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const currentStatus = searchParams.get("status") || "all";
  const currentType = searchParams.get("type") || "all";
  const currentPriority = searchParams.get("priority") || "all";
  const currentAssignee = searchParams.get("assignee") || "all";

  const updateFilter = (key: string, value: string) => {
    const params = new URLSearchParams(searchParams);
    if (value === "all") {
      params.delete(key);
    } else {
      params.set(key, value);
    }
    router.push(`/projects?${params.toString()}`);
  };

  const clearAllFilters = () => {
    router.push("/projects");
  };

  const hasActiveFilters =
    currentStatus !== "all" ||
    currentType !== "all" ||
    currentPriority !== "all" ||
    currentAssignee !== "all";

  const activeFilterCount = [
    currentStatus,
    currentType,
    currentPriority,
    currentAssignee,
  ].filter((f) => f !== "all").length;

  return (
    <Card className="p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">Filters</span>
          {activeFilterCount > 0 && (
            <Badge variant="secondary">{activeFilterCount} active</Badge>
          )}
        </div>

        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="text-muted-foreground hover:text-foreground"
          >
            <X className="h-3 w-3 mr-1" />
            Clear all
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Status Filter */}
        <div className="space-y-2">
          <label className="text-xs font-medium text-muted-foreground flex items-center gap-1">
            <Building2 className="h-3 w-3" />
            Status
          </label>
          <Select
            value={currentStatus}
            onValueChange={(value) => updateFilter("status", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="All Statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="in_progress">Active</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="on_hold">On Hold</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Project Type Filter */}
        <div className="space-y-2">
          <label className="text-xs font-medium text-muted-foreground flex items-center gap-1">
            <Building2 className="h-3 w-3" />
            Type
          </label>
          <Select
            value={currentType}
            onValueChange={(value) => updateFilter("type", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="All Types" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="residential_construction">
                Residential
              </SelectItem>
              <SelectItem value="commercial_construction">
                Commercial
              </SelectItem>
              <SelectItem value="renovation">Renovation</SelectItem>
              <SelectItem value="infrastructure">Infrastructure</SelectItem>
              <SelectItem value="maintenance">Maintenance</SelectItem>
              <SelectItem value="custom">Custom</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Priority Filter */}
        <div className="space-y-2">
          <label className="text-xs font-medium text-muted-foreground flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            Priority
          </label>
          <Select
            value={currentPriority}
            onValueChange={(value) => updateFilter("priority", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="All Priorities" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Priorities</SelectItem>
              <SelectItem value="critical">Critical</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="low">Low</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Project Manager Filter */}
        <div className="space-y-2">
          <label className="text-xs font-medium text-muted-foreground flex items-center gap-1">
            <Users className="h-3 w-3" />
            Manager
          </label>
          <Select
            value={currentAssignee}
            onValueChange={(value) => updateFilter("assignee", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="All Managers" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Managers</SelectItem>
              <SelectItem value="me">My Projects</SelectItem>
              {/* These would be populated from actual team members */}
              <SelectItem value="john-smith">John Smith</SelectItem>
              <SelectItem value="jane-doe">Jane Doe</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="mt-4 pt-4 border-t">
          <div className="flex items-center gap-2 flex-wrap">
            <span className="text-xs text-muted-foreground">
              Active filters:
            </span>

            {currentStatus !== "all" && (
              <Badge variant="outline" className="gap-1">
                Status:{" "}
                {currentStatus === "in_progress"
                  ? "Active"
                  : currentStatus.replace("_", " ")}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 ml-1"
                  onClick={() => updateFilter("status", "all")}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            )}

            {currentType !== "all" && (
              <Badge variant="outline" className="gap-1">
                Type: {currentType.replace("_", " ")}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 ml-1"
                  onClick={() => updateFilter("type", "all")}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            )}

            {currentPriority !== "all" && (
              <Badge variant="outline" className="gap-1">
                Priority: {currentPriority}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 ml-1"
                  onClick={() => updateFilter("priority", "all")}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            )}

            {currentAssignee !== "all" && (
              <Badge variant="outline" className="gap-1">
                Manager:{" "}
                {currentAssignee === "me"
                  ? "My Projects"
                  : currentAssignee.replace("-", " ")}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 ml-1"
                  onClick={() => updateFilter("assignee", "all")}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            )}
          </div>
        </div>
      )}

      {/* Quick Filter Buttons */}
      <div className="mt-4 pt-4 border-t">
        <div className="flex items-center gap-2 flex-wrap">
          <span className="text-xs text-muted-foreground">Quick filters:</span>

          <Button
            variant="outline"
            size="sm"
            onClick={() => updateFilter("status", "in_progress")}
            className={
              currentStatus === "in_progress"
                ? "bg-blue-50 border-blue-200"
                : ""
            }
          >
            Active Projects
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => updateFilter("priority", "critical")}
            className={
              currentPriority === "critical" ? "bg-red-50 border-red-200" : ""
            }
          >
            Critical Priority
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => updateFilter("assignee", "me")}
            className={
              currentAssignee === "me" ? "bg-green-50 border-green-200" : ""
            }
          >
            My Projects
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              updateFilter("status", "in_progress");
              updateFilter("priority", "high");
            }}
          >
            High Priority Active
          </Button>
        </div>
      </div>
    </Card>
  );
}
