"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@constru/ui/avatar";
import { Badge } from "@constru/ui/badge";
import { <PERSON><PERSON> } from "@constru/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@constru/ui/card";
import { cn } from "@constru/ui/cn";
import { Progress } from "@constru/ui/progress";
import { ScrollArea } from "@constru/ui/scroll-area";
import { Separator } from "@constru/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@constru/ui/tabs";
import { AnimatePresence, motion } from "framer-motion";
import {
  Activity,
  AlertCircle,
  AlertTriangle,
  ArrowDownRight,
  ArrowUpRight,
  BarChart3,
  Bell,
  BrainCircuit,
  Briefcase,
  Building2,
  Calendar,
  CheckCircle2,
  ChevronRight,
  Clock,
  Cloud,
  CloudRain,
  Crown,
  DollarSign,
  Download,
  ExternalLink,
  Eye,
  FileText,
  Filter,
  FolderKanban,
  Gauge,
  HardHat,
  Home,
  LayoutDashboard,
  Mail,
  MapPin,
  MessageSquare,
  Minus,
  Phone,
  Plus,
  Rocket,
  Search,
  Settings,
  Shield,
  Sparkles,
  Sun,
  Target,
  Thermometer,
  TrendingDown,
  TrendingUp,
  Upload,
  Users,
  Wind,
  XCircle,
  Zap,
} from "lucide-react";
import { useCallback, useMemo, useState } from "react";

import { AIInsightsPanel } from "./ai-insights-panel";
import { CommunicationHub } from "./communication-hub";
import { ProjectAnalytics } from "./project-analytics";
import { ProjectListView } from "./project-list-view";
// Import sub-components
import { ProjectOverviewDashboard } from "./project-overview-dashboard";
import { ResourceAllocationView } from "./resource-allocation-view";
import { SafetyQualityView } from "./safety-quality-view";
import { TeamManagementView } from "./team-management-view";

interface ProjectManagementHubProps {
  userRole:
    | "project_manager"
    | "foreman"
    | "worker"
    | "client"
    | "supervisor"
    | "admin"
    | "subcontractor";
  userId: string;
  teamId: string;
  userName: string;
}

type TabView =
  | "overview"
  | "projects"
  | "team"
  | "resources"
  | "analytics"
  | "communications"
  | "safety"
  | "insights";

// Role-based configuration
const roleConfigs = {
  admin: {
    title: "Administrator Control Center",
    subtitle: "Complete system oversight and management",
    icon: Crown,
    color: "from-purple-600 to-purple-700",
    textColor: "text-purple-600",
    bgPattern:
      "bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/20",
    availableTabs: [
      "overview",
      "projects",
      "team",
      "resources",
      "analytics",
      "communications",
      "safety",
      "insights",
    ] as TabView[],
    defaultTab: "overview" as TabView,
    permissions: {
      canCreateProject: true,
      canManageTeam: true,
      canViewFinancials: true,
      canManageResources: true,
      canAccessAnalytics: true,
      canManageSafety: true,
      canViewInsights: true,
      canExportData: true,
    },
  },
  project_manager: {
    title: "Project Management Dashboard",
    subtitle: "Comprehensive project oversight and control",
    icon: Briefcase,
    color: "from-blue-600 to-blue-700",
    textColor: "text-blue-600",
    bgPattern:
      "bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/20",
    availableTabs: [
      "overview",
      "projects",
      "team",
      "resources",
      "analytics",
      "communications",
      "insights",
    ] as TabView[],
    defaultTab: "overview" as TabView,
    permissions: {
      canCreateProject: true,
      canManageTeam: true,
      canViewFinancials: true,
      canManageResources: true,
      canAccessAnalytics: true,
      canManageSafety: false,
      canViewInsights: true,
      canExportData: true,
    },
  },
  supervisor: {
    title: "Supervisor Dashboard",
    subtitle: "Quality control and safety oversight",
    icon: Shield,
    color: "from-green-600 to-green-700",
    textColor: "text-green-600",
    bgPattern:
      "bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/20",
    availableTabs: [
      "overview",
      "projects",
      "team",
      "safety",
      "communications",
    ] as TabView[],
    defaultTab: "safety" as TabView,
    permissions: {
      canCreateProject: false,
      canManageTeam: false,
      canViewFinancials: false,
      canManageResources: false,
      canAccessAnalytics: false,
      canManageSafety: true,
      canViewInsights: false,
      canExportData: true,
    },
  },
  foreman: {
    title: "Foreman Field Dashboard",
    subtitle: "Task management and team coordination",
    icon: HardHat,
    color: "from-orange-600 to-orange-700",
    textColor: "text-orange-600",
    bgPattern:
      "bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/20 dark:to-orange-900/20",
    availableTabs: [
      "overview",
      "projects",
      "team",
      "resources",
      "communications",
    ] as TabView[],
    defaultTab: "projects" as TabView,
    permissions: {
      canCreateProject: false,
      canManageTeam: false,
      canViewFinancials: false,
      canManageResources: true,
      canAccessAnalytics: false,
      canManageSafety: false,
      canViewInsights: false,
      canExportData: false,
    },
  },
  worker: {
    title: "My Work Dashboard",
    subtitle: "Tasks and assignments",
    icon: Users,
    color: "from-gray-600 to-gray-700",
    textColor: "text-gray-600",
    bgPattern:
      "bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-950/20 dark:to-gray-900/20",
    availableTabs: ["overview", "projects", "communications"] as TabView[],
    defaultTab: "overview" as TabView,
    permissions: {
      canCreateProject: false,
      canManageTeam: false,
      canViewFinancials: false,
      canManageResources: false,
      canAccessAnalytics: false,
      canManageSafety: false,
      canViewInsights: false,
      canExportData: false,
    },
  },
  client: {
    title: "Project Portal",
    subtitle: "Track your construction projects",
    icon: Home,
    color: "from-emerald-600 to-emerald-700",
    textColor: "text-emerald-600",
    bgPattern:
      "bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-950/20 dark:to-emerald-900/20",
    availableTabs: [
      "overview",
      "projects",
      "analytics",
      "communications",
    ] as TabView[],
    defaultTab: "overview" as TabView,
    permissions: {
      canCreateProject: false,
      canManageTeam: false,
      canViewFinancials: true,
      canManageResources: false,
      canAccessAnalytics: true,
      canManageSafety: false,
      canViewInsights: false,
      canExportData: false,
    },
  },
  subcontractor: {
    title: "Subcontractor Portal",
    subtitle: "Specialized task management",
    icon: Briefcase,
    color: "from-indigo-600 to-indigo-700",
    textColor: "text-indigo-600",
    bgPattern:
      "bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-950/20 dark:to-indigo-900/20",
    availableTabs: [
      "overview",
      "projects",
      "resources",
      "communications",
    ] as TabView[],
    defaultTab: "projects" as TabView,
    permissions: {
      canCreateProject: false,
      canManageTeam: false,
      canViewFinancials: false,
      canManageResources: true,
      canAccessAnalytics: false,
      canManageSafety: false,
      canViewInsights: false,
      canExportData: false,
    },
  },
};

export function ProjectManagementHub({
  userRole,
  userId,
  teamId,
  userName,
}: ProjectManagementHubProps) {
  const roleConfig = roleConfigs[userRole];
  const [activeTab, setActiveTab] = useState<TabView>(roleConfig.defaultTab);
  const [showNotifications, setShowNotifications] = useState(false);

  // Mock data for demonstration - in production, this would come from API
  const [notifications] = useState([
    {
      id: "1",
      type: "alert",
      title: "Budget Alert",
      message: "Downtown Tower project is approaching 90% budget utilization",
      time: "5 minutes ago",
      priority: "high",
    },
    {
      id: "2",
      type: "task",
      title: "Task Completed",
      message: "Foundation inspection completed for Riverside Complex",
      time: "1 hour ago",
      priority: "medium",
    },
    {
      id: "3",
      type: "weather",
      title: "Weather Advisory",
      message: "Rain expected tomorrow - outdoor work may be affected",
      time: "2 hours ago",
      priority: "low",
    },
  ]);

  const [weatherData] = useState({
    current: "sunny",
    temperature: 72,
    condition: "Clear",
    forecast: [
      { day: "Today", high: 75, low: 60, condition: "sunny" },
      { day: "Tomorrow", high: 68, low: 55, condition: "rainy" },
      { day: "Thursday", high: 70, low: 58, condition: "cloudy" },
    ],
  });

  // Tab icon mapping
  const tabIcons = {
    overview: LayoutDashboard,
    projects: FolderKanban,
    team: Users,
    resources: Gauge,
    analytics: BarChart3,
    communications: MessageSquare,
    safety: Shield,
    insights: BrainCircuit,
  };

  const getWeatherIcon = (condition: string) => {
    switch (condition) {
      case "sunny":
        return Sun;
      case "cloudy":
        return Cloud;
      case "rainy":
        return CloudRain;
      case "windy":
        return Wind;
      default:
        return Sun;
    }
  };

  const WeatherIcon = getWeatherIcon(weatherData.current);

  return (
    <div className="min-h-screen bg-background">
      {/* Enhanced Header with Role-Based Styling */}
      <div className={cn("relative overflow-hidden", roleConfig.bgPattern)}>
        <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,transparent,rgba(255,255,255,0.6))]" />

        <div className="relative px-6 py-8">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
              {/* Left Section - Title and Role Info */}
              <div className="flex items-start gap-4">
                <div
                  className={cn(
                    "p-3 rounded-xl bg-gradient-to-br shadow-lg",
                    roleConfig.color,
                  )}
                >
                  <roleConfig.icon className="h-8 w-8 text-white" />
                </div>

                <div>
                  <h1 className="text-3xl font-bold tracking-tight">
                    {roleConfig.title}
                  </h1>
                  <p className="text-muted-foreground mt-1">
                    {roleConfig.subtitle}
                  </p>
                  <div className="flex items-center gap-4 mt-3">
                    <Badge variant="secondary" className="gap-1">
                      <Eye className="h-3 w-3" />
                      {userName}
                    </Badge>
                    <Badge
                      variant="outline"
                      className={cn("gap-1", roleConfig.textColor)}
                    >
                      <roleConfig.icon className="h-3 w-3" />
                      {userRole.replace("_", " ").charAt(0).toUpperCase() +
                        userRole.replace("_", " ").slice(1)}
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Right Section - Actions and Quick Info */}
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                {/* Weather Widget */}
                <Card className="p-3 bg-background/50 backdrop-blur border-white/20">
                  <div className="flex items-center gap-3">
                    <WeatherIcon className="h-8 w-8 text-yellow-500" />
                    <div>
                      <p className="text-2xl font-semibold">
                        {weatherData.temperature}°F
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {weatherData.condition}
                      </p>
                    </div>
                  </div>
                </Card>

                {/* Notifications */}
                <div className="relative">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setShowNotifications(!showNotifications)}
                    className="relative"
                  >
                    <Bell className="h-4 w-4" />
                    {notifications.length > 0 && (
                      <span className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center">
                        {notifications.length}
                      </span>
                    )}
                  </Button>

                  {showNotifications && (
                    <Card className="absolute right-0 top-12 w-80 z-50 shadow-lg">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm">Notifications</CardTitle>
                      </CardHeader>
                      <CardContent className="p-0">
                        <ScrollArea className="h-64">
                          {notifications.map((notif, idx) => (
                            <div key={notif.id}>
                              <div className="p-3 hover:bg-muted/50 cursor-pointer transition-colors">
                                <div className="flex items-start gap-3">
                                  <div
                                    className={cn(
                                      "p-2 rounded-lg",
                                      notif.priority === "high"
                                        ? "bg-red-100 text-red-600 dark:bg-red-900/20"
                                        : notif.priority === "medium"
                                          ? "bg-yellow-100 text-yellow-600 dark:bg-yellow-900/20"
                                          : "bg-blue-100 text-blue-600 dark:bg-blue-900/20",
                                    )}
                                  >
                                    {notif.type === "alert" ? (
                                      <AlertTriangle className="h-4 w-4" />
                                    ) : notif.type === "task" ? (
                                      <CheckCircle2 className="h-4 w-4" />
                                    ) : (
                                      <Cloud className="h-4 w-4" />
                                    )}
                                  </div>
                                  <div className="flex-1">
                                    <p className="text-sm font-medium">
                                      {notif.title}
                                    </p>
                                    <p className="text-xs text-muted-foreground mt-1">
                                      {notif.message}
                                    </p>
                                    <p className="text-xs text-muted-foreground mt-2">
                                      {notif.time}
                                    </p>
                                  </div>
                                </div>
                              </div>
                              {idx < notifications.length - 1 && <Separator />}
                            </div>
                          ))}
                        </ScrollArea>
                      </CardContent>
                    </Card>
                  )}
                </div>

                {/* Quick Actions */}
                <div className="flex items-center gap-2">
                  {roleConfig.permissions.canExportData && (
                    <Button variant="outline" size="sm">
                      <Download className="mr-2 h-4 w-4" />
                      Export
                    </Button>
                  )}
                  {roleConfig.permissions.canCreateProject && (
                    <Button
                      size="sm"
                      className={cn("shadow-lg", roleConfig.color)}
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      New Project
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area with Enhanced Navigation */}
      <div className="px-6 py-6">
        <div className="max-w-7xl mx-auto">
          <Tabs
            value={activeTab}
            onValueChange={(value) => setActiveTab(value as TabView)}
          >
            {/* Enhanced Tab Navigation */}
            <div className="mb-6">
              <TabsList className="w-full justify-start h-auto p-1 bg-muted/50">
                {roleConfig.availableTabs.map((tab) => {
                  const Icon = tabIcons[tab];
                  return (
                    <TabsTrigger
                      key={tab}
                      value={tab}
                      className="flex items-center gap-2 px-4 py-2.5 data-[state=active]:bg-background data-[state=active]:shadow-sm"
                    >
                      <Icon className="h-4 w-4" />
                      <span className="capitalize">{tab}</span>
                    </TabsTrigger>
                  );
                })}
              </TabsList>
            </div>

            {/* Tab Content with Animation */}
            <AnimatePresence mode="wait">
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
              >
                <TabsContent value="overview" className="space-y-6 mt-0">
                  <ProjectOverviewDashboard
                    userRole={userRole}
                    userId={userId}
                    teamId={teamId}
                    permissions={roleConfig.permissions}
                  />
                </TabsContent>

                <TabsContent value="projects" className="space-y-6 mt-0">
                  <ProjectListView
                    userRole={userRole}
                    userId={userId}
                    teamId={teamId}
                    permissions={roleConfig.permissions}
                  />
                </TabsContent>

                <TabsContent value="team" className="space-y-6 mt-0">
                  <TeamManagementView
                    userRole={userRole}
                    teamId={teamId}
                    permissions={roleConfig.permissions}
                  />
                </TabsContent>

                <TabsContent value="resources" className="space-y-6 mt-0">
                  <ResourceAllocationView
                    userRole={userRole}
                    teamId={teamId}
                    permissions={roleConfig.permissions}
                  />
                </TabsContent>

                <TabsContent value="analytics" className="space-y-6 mt-0">
                  <ProjectAnalytics
                    userRole={userRole}
                    teamId={teamId}
                    permissions={roleConfig.permissions}
                  />
                </TabsContent>

                <TabsContent value="communications" className="space-y-6 mt-0">
                  <CommunicationHub
                    userRole={userRole}
                    userId={userId}
                    teamId={teamId}
                    permissions={roleConfig.permissions}
                  />
                </TabsContent>

                <TabsContent value="safety" className="space-y-6 mt-0">
                  <SafetyQualityView
                    userRole={userRole}
                    teamId={teamId}
                    permissions={roleConfig.permissions}
                  />
                </TabsContent>

                <TabsContent value="insights" className="space-y-6 mt-0">
                  <AIInsightsPanel
                    userRole={userRole}
                    teamId={teamId}
                    permissions={roleConfig.permissions}
                  />
                </TabsContent>
              </motion.div>
            </AnimatePresence>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
