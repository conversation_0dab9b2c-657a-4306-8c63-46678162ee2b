"use client";

import { useTRPC } from "@/trpc/client";
import { Badge } from "@constru/ui/badge";
import { <PERSON><PERSON> } from "@constru/ui/button";
import { Card } from "@constru/ui/card";
import { Input } from "@constru/ui/input";
import { Skeleton } from "@constru/ui/skeleton";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@constru/ui/tabs";
import {
  Calendar,
  Filter,
  Grid3X3,
  Kanban,
  List,
  Plus,
  Search,
} from "lucide-react";
import { useState } from "react";
import { ProjectCalendarView } from "./project-calendar-view";
import { ProjectCard } from "./project-card";
import { ProjectKanbanView } from "./project-kanban-view";
import { ProjectListView } from "./project-list-view";

type ViewMode = "grid" | "list" | "kanban" | "calendar";

export function ProjectDashboard() {
  const trpc = useTRPC();
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");

  const {
    data: projects,
    isLoading,
    error,
  } = trpc.projectManagement.dashboard.useQuery({
    projectIds: undefined, // Optional: specify specific project IDs
    status: statusFilter !== "all" ? (statusFilter as 'in_progress' | 'completed') : undefined,
    limit: 50,
  });

  const { data: userRole } = trpc.user.get.useQuery();

  if (isLoading) {
    return <ProjectDashboardSkeleton />;
  }

  if (error) {
    return (
      <Card className="p-6 text-center">
        <p className="text-muted-foreground">
          Failed to load projects. Please try again.
        </p>
      </Card>
    );
  }

  if (!projects?.length) {
    return <EmptyProjectState />;
  }

  // Filter projects based on search and status
  const filteredProjects = projects.filter((project) => {
    const matchesSearch =
      !searchQuery ||
      project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.customer?.name?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus =
      statusFilter === "all" || project.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Get status counts for filter badges
  const statusCounts = projects.reduce(
    (acc, project) => {
      acc[project.status] = (acc[project.status] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>,
  );

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-4 flex-1">
          <div className="relative max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search projects..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Status Filter */}
          <div className="flex items-center gap-2">
            <Button
              variant={statusFilter === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter("all")}
            >
              All
              <Badge variant="secondary" className="ml-2">
                {projects.length}
              </Badge>
            </Button>
            <Button
              variant={statusFilter === "in_progress" ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter("in_progress")}
            >
              Active
              <Badge variant="secondary" className="ml-2">
                {statusCounts.in_progress || 0}
              </Badge>
            </Button>
            <Button
              variant={statusFilter === "completed" ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter("completed")}
            >
              Completed
              <Badge variant="secondary" className="ml-2">
                {statusCounts.completed || 0}
              </Badge>
            </Button>
          </div>
        </div>

        {/* View Mode Switcher */}
        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === "grid" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("grid")}
          >
            <Grid3X3 className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === "list" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("list")}
          >
            <List className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === "kanban" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("kanban")}
          >
            <Kanban className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === "calendar" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("calendar")}
          >
            <Calendar className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Results Count */}
      <div className="text-sm text-muted-foreground">
        Showing {filteredProjects.length} of {projects.length} projects
      </div>

      {/* Project Views */}
      {viewMode === "grid" && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProjects.map((project) => (
            <ProjectCard
              key={project.id}
              project={project}
              userRole={userRole?.role}
            />
          ))}
        </div>
      )}

      {viewMode === "list" && (
        <ProjectListView
          projects={filteredProjects}
          userRole={userRole?.role}
        />
      )}

      {viewMode === "kanban" && (
        <ProjectKanbanView
          projects={filteredProjects}
          userRole={userRole?.role}
        />
      )}

      {viewMode === "calendar" && (
        <ProjectCalendarView
          projects={filteredProjects}
          userRole={userRole?.role}
        />
      )}
    </div>
  );
}

function ProjectDashboardSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header Skeleton */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-4 w-48" />
        </div>
        <Skeleton className="h-10 w-32" />
      </div>

      {/* Filters Skeleton */}
      <div className="flex items-center gap-4">
        <Skeleton className="h-10 w-64" />
        <Skeleton className="h-10 w-24" />
        <Skeleton className="h-10 w-24" />
        <Skeleton className="h-10 w-24" />
      </div>

      {/* Grid Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i} className="p-6">
            <div className="space-y-4">
              <div className="flex items-start justify-between">
                <Skeleton className="h-6 w-48" />
                <Skeleton className="h-6 w-16" />
              </div>
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <div className="flex items-center gap-2">
                <Skeleton className="h-8 w-16" />
                <Skeleton className="h-8 w-20" />
              </div>
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-16" />
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}

function EmptyProjectState() {
  return (
    <Card className="p-12 text-center">
      <div className="mx-auto w-48 h-48 mb-6 opacity-20">
        <svg viewBox="0 0 24 24" fill="none" className="w-full h-full">
          <path
            d="M3 7V17C3 18.1046 3.89543 19 5 19H19C20.1046 19 21 18.1046 21 17V7M3 7L12 2L21 7M3 7V9H21V7"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M8 11V15M12 11V15M16 11V15"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
          />
        </svg>
      </div>
      <h3 className="text-lg font-semibold mb-2">No Projects Yet</h3>
      <p className="text-muted-foreground mb-6">
        Get started by creating your first construction project
      </p>
      <Button>
        <Plus className="mr-2 h-4 w-4" />
        Create Project
      </Button>
    </Card>
  );
}
