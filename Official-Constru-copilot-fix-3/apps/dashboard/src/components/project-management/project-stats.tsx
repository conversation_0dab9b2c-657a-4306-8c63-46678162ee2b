"use client";

import { formatAmount } from "@/utils/format";
import { Badge } from "@constru/ui/badge";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>H<PERSON>er, CardTitle } from "@constru/ui/card";
import { cn } from "@constru/ui/cn";
import { Progress } from "@constru/ui/progress";
import {
  AlertTriangle,
  Building2,
  Calendar,
  CheckCircle,
  Clock,
  DollarSign,
  Timer,
  TrendingDown,
  TrendingUp,
  Users,
} from "lucide-react";

interface ProjectStatsProps {
  projects: Array<{
    id: string;
    status: string;
    projectType?: string;
    budget?: number;
    actualCost?: number;
    completionPercentage?: number;
    totalTasks?: number;
    completedTasks?: number;
    overdueTasks?: number;
    teamSize?: number;
    plannedEndDate?: string;
  }>;
}

export function ProjectStats({ projects }: ProjectStatsProps) {
  // Calculate overall metrics
  const totalProjects = projects.length;
  const activeProjects = projects.filter(
    (p) => p.status === "in_progress",
  ).length;
  const completedProjects = projects.filter(
    (p) => p.status === "completed",
  ).length;
  const onHoldProjects = projects.filter((p) => p.status === "on_hold").length;

  // Financial metrics
  const totalBudget = projects.reduce((sum, p) => sum + (p.budget || 0), 0);
  const totalSpent = projects.reduce((sum, p) => sum + (p.actualCost || 0), 0);
  const budgetUtilization =
    totalBudget > 0 ? (totalSpent / totalBudget) * 100 : 0;

  // Task metrics
  const totalTasks = projects.reduce((sum, p) => sum + (p.totalTasks || 0), 0);
  const completedTasks = projects.reduce(
    (sum, p) => sum + (p.completedTasks || 0),
    0,
  );
  const overdueTasks = projects.reduce(
    (sum, p) => sum + (p.overdueTasks || 0),
    0,
  );
  const taskCompletionRate =
    totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

  // Team metrics
  const totalTeamMembers = projects.reduce(
    (sum, p) => sum + (p.teamSize || 0),
    0,
  );
  const avgTeamSize = totalProjects > 0 ? totalTeamMembers / totalProjects : 0;

  // Project health metrics
  const projectsOverBudget = projects.filter(
    (p) => p.budget && p.actualCost && p.actualCost > p.budget,
  ).length;

  const projectsOverdue = projects.filter((p) => {
    if (!p.plannedEndDate || p.status === "completed") return false;
    return new Date() > new Date(p.plannedEndDate);
  }).length;

  // Average completion percentage
  const avgCompletion =
    projects.length > 0
      ? projects.reduce((sum, p) => sum + (p.completionPercentage || 0), 0) /
        projects.length
      : 0;

  const statCards = [
    {
      title: "Active Projects",
      value: activeProjects,
      total: totalProjects,
      icon: Building2,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      description: `${completedProjects} completed, ${onHoldProjects} on hold`,
      trend: activeProjects > completedProjects ? "up" : "down",
    },
    {
      title: "Budget Overview",
      value: formatAmount({ amount: totalSpent, currency: "USD" }),
      subtitle: `of ${formatAmount({ amount: totalBudget, currency: "USD" })}`,
      icon: DollarSign,
      color:
        budgetUtilization > 100
          ? "text-red-600"
          : budgetUtilization > 80
            ? "text-yellow-600"
            : "text-green-600",
      bgColor:
        budgetUtilization > 100
          ? "bg-red-50"
          : budgetUtilization > 80
            ? "bg-yellow-50"
            : "bg-green-50",
      description: `${budgetUtilization.toFixed(1)}% utilized`,
      progress: Math.min(budgetUtilization, 100),
      trend: budgetUtilization > 90 ? "down" : "up",
    },
    {
      title: "Task Progress",
      value: `${completedTasks}/${totalTasks}`,
      icon: CheckCircle,
      color:
        taskCompletionRate > 80
          ? "text-green-600"
          : taskCompletionRate > 60
            ? "text-yellow-600"
            : "text-red-600",
      bgColor:
        taskCompletionRate > 80
          ? "bg-green-50"
          : taskCompletionRate > 60
            ? "bg-yellow-50"
            : "bg-red-50",
      description: `${taskCompletionRate.toFixed(1)}% completion rate`,
      progress: taskCompletionRate,
      warning: overdueTasks > 0 ? `${overdueTasks} overdue tasks` : undefined,
      trend: taskCompletionRate > 75 ? "up" : "down",
    },
    {
      title: "Team Members",
      value: totalTeamMembers,
      subtitle: `Avg ${avgTeamSize.toFixed(1)} per project`,
      icon: Users,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      description: `${activeProjects} active projects`,
      trend: "up",
    },
  ];

  // Alert indicators
  const alerts = [
    ...(projectsOverBudget > 0
      ? [
          {
            type: "budget",
            count: projectsOverBudget,
            message: `${projectsOverBudget} project${projectsOverBudget > 1 ? "s" : ""} over budget`,
            color: "text-red-600",
          },
        ]
      : []),
    ...(projectsOverdue > 0
      ? [
          {
            type: "timeline",
            count: projectsOverdue,
            message: `${projectsOverdue} project${projectsOverdue > 1 ? "s" : ""} overdue`,
            color: "text-red-600",
          },
        ]
      : []),
    ...(overdueTasks > 0
      ? [
          {
            type: "tasks",
            count: overdueTasks,
            message: `${overdueTasks} task${overdueTasks > 1 ? "s" : ""} overdue`,
            color: "text-yellow-600",
          },
        ]
      : []),
  ];

  return (
    <div className="space-y-6">
      {/* Alert Banner */}
      {alerts.length > 0 && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="pt-6">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div className="space-y-1">
                <h4 className="font-medium text-yellow-800">
                  Attention Required
                </h4>
                <div className="space-y-1">
                  {alerts.map((alert, index) => (
                    <p key={index} className={cn("text-sm", alert.color)}>
                      {alert.message}
                    </p>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => (
          <Card key={index} className="relative overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {stat.title}
              </CardTitle>
              <div className={cn("p-2 rounded-md", stat.bgColor)}>
                <stat.icon className={cn("h-4 w-4", stat.color)} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {/* Main Value */}
                <div className="space-y-1">
                  <div className="flex items-baseline gap-2">
                    <div className="text-2xl font-bold">{stat.value}</div>
                    {stat.subtitle && (
                      <div className="text-sm text-muted-foreground">
                        {stat.subtitle}
                      </div>
                    )}
                    {stat.trend && (
                      <div
                        className={cn(
                          "flex items-center text-xs",
                          stat.trend === "up"
                            ? "text-green-600"
                            : "text-red-600",
                        )}
                      >
                        {stat.trend === "up" ? (
                          <TrendingUp className="h-3 w-3" />
                        ) : (
                          <TrendingDown className="h-3 w-3" />
                        )}
                      </div>
                    )}
                  </div>

                  <p className="text-xs text-muted-foreground">
                    {stat.description}
                  </p>
                </div>

                {/* Progress Bar */}
                {stat.progress !== undefined && (
                  <div className="space-y-1">
                    <Progress value={stat.progress} className="h-2" />
                  </div>
                )}

                {/* Warning */}
                {stat.warning && (
                  <div className="flex items-center gap-2 p-2 bg-yellow-50 rounded-md">
                    <AlertTriangle className="h-3 w-3 text-yellow-600" />
                    <span className="text-xs text-yellow-700">
                      {stat.warning}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Project Completion Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Project Progress Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">
                Overall Completion
              </span>
              <span className="font-medium">{avgCompletion.toFixed(1)}%</span>
            </div>
            <Progress value={avgCompletion} className="h-3" />

            <div className="grid grid-cols-3 gap-4 pt-4 border-t">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {activeProjects}
                </div>
                <div className="text-xs text-muted-foreground">Active</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {completedProjects}
                </div>
                <div className="text-xs text-muted-foreground">Completed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {onHoldProjects}
                </div>
                <div className="text-xs text-muted-foreground">On Hold</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
