"use client";

import { Badge } from "@constru/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@constru/ui/card";
import { HardHat, Shield } from "lucide-react";

interface SafetyQualityViewProps {
  userRole: string;
  teamId: string;
  permissions: any;
}

export function SafetyQualityView({
  userRole,
  teamId,
  permissions,
}: SafetyQualityViewProps) {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Shield className="h-6 w-6 text-primary" />
          <div>
            <h2 className="text-2xl font-bold">Safety & Quality</h2>
            <p className="text-muted-foreground">
              Safety management and quality control
            </p>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Safety & Quality Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <HardHat className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              Safety & Quality System Coming Soon
            </h3>
            <p className="text-muted-foreground">
              Comprehensive safety incident tracking, quality inspections, and
              compliance management.
            </p>
            <Badge variant="secondary" className="mt-4">
              In Development
            </Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
