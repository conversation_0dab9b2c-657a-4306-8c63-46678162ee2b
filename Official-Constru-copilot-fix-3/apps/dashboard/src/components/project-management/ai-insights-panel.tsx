"use client";

import { Badge } from "@constru/ui/badge";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@constru/ui/card";
import { BrainCircuit, Sparkles } from "lucide-react";

interface AIInsightsPanelProps {
  userRole: string;
  teamId: string;
  permissions: any;
}

export function AIInsightsPanel({
  userRole,
  teamId,
  permissions,
}: AIInsightsPanelProps) {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <BrainCircuit className="h-6 w-6 text-primary" />
          <div>
            <h2 className="text-2xl font-bold">AI Insights</h2>
            <p className="text-muted-foreground">
              Intelligent project analysis and recommendations
            </p>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>AI-Powered Analytics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <Sparkles className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              AI Insights Coming Soon
            </h3>
            <p className="text-muted-foreground">
              Machine learning powered project predictions, risk analysis, and
              optimization recommendations.
            </p>
            <Badge variant="secondary" className="mt-4">
              In Development
            </Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
