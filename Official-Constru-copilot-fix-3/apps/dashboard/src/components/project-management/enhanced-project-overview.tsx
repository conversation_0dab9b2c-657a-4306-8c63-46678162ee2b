"use client";

import { useTR<PERSON> } from "@/trpc/client";
import { formatAmount } from "@/utils/format";
import { Avatar, AvatarFallback, AvatarImage } from "@constru/ui/avatar";
import { Badge } from "@constru/ui/badge";
import { <PERSON><PERSON> } from "@constru/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@constru/ui/card";
import { cn } from "@constru/ui/cn";
import { Progress } from "@constru/ui/progress";
import { Separator } from "@constru/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@constru/ui/tabs";
import { useQuery } from "@tanstack/react-query";
import {
  differenceInDays,
  differenceInWeeks,
  format,
  isAfter,
  isBefore,
} from "date-fns";
import {
  Activity,
  AlertTriangle,
  BarChart3,
  Building2,
  Calendar,
  Camera,
  CheckCircle,
  Circle,
  Clipboard,
  Clock,
  DollarSign,
  Edit,
  ExternalLink,
  Eye,
  FileText,
  Flag,
  Mail,
  MapPin,
  MessageSquare,
  Minus,
  Phone,
  Plus,
  Settings,
  Shield,
  Star,
  Target,
  Timer,
  TrendingDown,
  TrendingUp,
  Users,
  Wrench,
  Zap,
} from "lucide-react";
import Link from "next/link";
import { useMemo, useState } from "react";

interface EnhancedProjectOverviewProps {
  projectId: string;
  userRole?:
    | "project_manager"
    | "foreman"
    | "worker"
    | "client"
    | "supervisor"
    | "admin"
    | "subcontractor";
  userId: string;
}

export function EnhancedProjectOverview({
  projectId,
  userRole = "worker",
  userId,
}: EnhancedProjectOverviewProps) {
  const [activeTab, setActiveTab] = useState("overview");

  // Fetch comprehensive project data
  const { data: projectDashboardData, isLoading: isDashboardLoading } =
    useQuery(
      trpc.projectManagement.dashboard.queryOptions({
        projectIds: [projectId],
        status: undefined,
        limit: 1,
      }),
    );

  const { data: projectTeam, isLoading: isTeamLoading } = useQuery({
    queryKey: ["projectTeam", projectId],
    queryFn: () => trpc.projectManagement.getTeam.query({ projectId }),
  });

  const { data: projectPhases, isLoading: isPhasesLoading } = useQuery({
    queryKey: ["projectPhases", projectId],
    queryFn: () => trpc.projectManagement.getPhases.query({ projectId }),
  });

  const { data: projectTasks, isLoading: isTasksLoading } = useQuery({
    queryKey: ["projectTasks", projectId],
    queryFn: () => trpc.projectManagement.getTasks.query({ projectId }),
  });

  const { data: projectResources, isLoading: isResourcesLoading } = useQuery({
    queryKey: ["projectResources", projectId],
    queryFn: () => trpc.projectManagement.getResources.query({ projectId }),
  });

  const { data: projectCommunications, isLoading: isCommunicationsLoading } =
    useQuery({
      queryKey: ["projectCommunications", projectId],
      queryFn: () =>
        trpc.projectManagement.getCommunications.query({ projectId, userId }),
    });

  const project = projectDashboardData?.[0];

  // Role-based permissions
  const permissions = useMemo(() => {
    const rolePermissions = {
      admin: {
        canEdit: true,
        canManageTeam: true,
        canViewFinancials: true,
        canManageResources: true,
        canCreateTasks: true,
        canViewAllCommunications: true,
        canManageSafety: true,
        canApproveChanges: true,
      },
      project_manager: {
        canEdit: true,
        canManageTeam: true,
        canViewFinancials: true,
        canManageResources: true,
        canCreateTasks: true,
        canViewAllCommunications: true,
        canManageSafety: true,
        canApproveChanges: true,
      },
      supervisor: {
        canEdit: true,
        canManageTeam: false,
        canViewFinancials: false,
        canManageResources: true,
        canCreateTasks: true,
        canViewAllCommunications: false,
        canManageSafety: true,
        canApproveChanges: false,
      },
      foreman: {
        canEdit: true,
        canManageTeam: false,
        canViewFinancials: false,
        canManageResources: false,
        canCreateTasks: true,
        canViewAllCommunications: false,
        canManageSafety: true,
        canApproveChanges: false,
      },
      subcontractor: {
        canEdit: false,
        canManageTeam: false,
        canViewFinancials: false,
        canManageResources: false,
        canCreateTasks: false,
        canViewAllCommunications: false,
        canManageSafety: false,
        canApproveChanges: false,
      },
      worker: {
        canEdit: false,
        canManageTeam: false,
        canViewFinancials: false,
        canManageResources: false,
        canCreateTasks: false,
        canViewAllCommunications: false,
        canManageSafety: false,
        canApproveChanges: false,
      },
      client: {
        canEdit: false,
        canManageTeam: false,
        canViewFinancials: true,
        canManageResources: false,
        canCreateTasks: false,
        canViewAllCommunications: false,
        canManageSafety: false,
        canApproveChanges: false,
      },
    };
    return rolePermissions[userRole] || rolePermissions.worker;
  }, [userRole]);

  // Calculate project metrics
  const projectMetrics = useMemo(() => {
    if (!project) return null;

    const today = new Date();
    const plannedEnd = project.plannedEndDate
      ? new Date(project.plannedEndDate)
      : null;
    const plannedStart = project.plannedStartDate
      ? new Date(project.plannedStartDate)
      : null;

    // Timeline calculations
    const isOverdue =
      plannedEnd &&
      isAfter(today, plannedEnd) &&
      project.status !== "completed";
    const daysRemaining = plannedEnd
      ? differenceInDays(plannedEnd, today)
      : null;
    const totalProjectDays =
      plannedStart && plannedEnd
        ? differenceInDays(plannedEnd, plannedStart)
        : null;
    const daysElapsed = plannedStart
      ? differenceInDays(today, plannedStart)
      : null;
    const timelineProgress =
      totalProjectDays && daysElapsed
        ? Math.max(0, Math.min(100, (daysElapsed / totalProjectDays) * 100))
        : 0;

    // Financial calculations
    const budgetUtilization =
      project.budget && project.actualCost
        ? (project.actualCost / project.budget) * 100
        : 0;
    const remainingBudget =
      project.budget && project.actualCost
        ? project.budget - project.actualCost
        : project.budget || 0;
    const budgetBurnRate =
      daysElapsed && project.actualCost ? project.actualCost / daysElapsed : 0;
    const projectedFinalCost =
      budgetBurnRate && totalProjectDays
        ? budgetBurnRate * totalProjectDays
        : project.actualCost || 0;

    // Task performance
    const taskCompletionRate =
      project.totalTasks > 0
        ? (project.completedTasks / project.totalTasks) * 100
        : 0;
    const taskEfficiency =
      timelineProgress > 0
        ? (project.completionPercentage || 0) / timelineProgress
        : 1;

    // Overall health score
    let healthScore = 100;
    if (budgetUtilization > 100) healthScore -= 30;
    else if (budgetUtilization > 80) healthScore -= 15;
    if (isOverdue) healthScore -= 25;
    else if (daysRemaining && daysRemaining < 7) healthScore -= 10;
    if (project.overdueTasks > 0)
      healthScore -= Math.min(project.overdueTasks * 5, 20);
    if (taskEfficiency < 0.8) healthScore -= 15;

    return {
      timeline: {
        isOverdue,
        daysRemaining,
        timelineProgress,
        daysElapsed,
        totalProjectDays,
      },
      financial: {
        budgetUtilization,
        remainingBudget,
        budgetBurnRate,
        projectedFinalCost,
        isOverBudget: budgetUtilization > 100,
        isBudgetWarning: budgetUtilization > 80,
      },
      tasks: {
        taskCompletionRate,
        taskEfficiency,
        productivityScore: Math.min(100, taskCompletionRate * taskEfficiency),
      },
      health: {
        score: Math.max(0, healthScore),
        status:
          healthScore >= 80
            ? "excellent"
            : healthScore >= 60
              ? "good"
              : healthScore >= 40
                ? "warning"
                : "critical",
      },
    };
  }, [project]);

  if (isDashboardLoading || !project) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary" />
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 border-green-200";
      case "in_progress":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "on_hold":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "cancelled":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getHealthColor = (status: string) => {
    switch (status) {
      case "excellent":
        return "text-green-600 bg-green-50 border-green-200";
      case "good":
        return "text-blue-600 bg-blue-50 border-blue-200";
      case "warning":
        return "text-yellow-600 bg-yellow-50 border-yellow-200";
      case "critical":
        return "text-red-600 bg-red-50 border-red-200";
      default:
        return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };

  return (
    <div className="space-y-6">
      {/* Project Header */}
      <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-3 mb-2">
            <Building2 className="h-8 w-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold text-foreground truncate">
                {project.name}
              </h1>
              {project.customer && (
                <p className="text-lg text-muted-foreground">
                  {project.customer.name}
                </p>
              )}
            </div>
          </div>

          {project.description && (
            <p className="text-muted-foreground mb-4 max-w-3xl">
              {project.description}
            </p>
          )}

          <div className="flex flex-wrap items-center gap-4">
            <Badge
              className={getStatusColor(project.status)}
              variant="secondary"
            >
              {project.status === "in_progress"
                ? "Active"
                : project.status === "completed"
                  ? "Completed"
                  : project.status.replace("_", " ")}
            </Badge>

            {project.priority && (
              <Badge variant="outline" className="capitalize">
                <Flag className="mr-1 h-3 w-3" />
                {project.priority} Priority
              </Badge>
            )}

            {project.projectType && (
              <Badge variant="outline" className="capitalize">
                {project.projectType.replace("_", " ")}
              </Badge>
            )}

            {projectMetrics && (
              <div
                className={cn(
                  "flex items-center gap-2 px-3 py-1 rounded-md border text-sm font-medium",
                  getHealthColor(projectMetrics.health.status),
                )}
              >
                <Zap className="h-3 w-3" />
                Health: {projectMetrics.health.score}%
              </div>
            )}
          </div>

          {project.locationAddress && (
            <div className="flex items-center gap-2 mt-4 text-muted-foreground">
              <MapPin className="h-4 w-4" />
              <span>{project.locationAddress}</span>
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="flex flex-col sm:flex-row gap-2">
          {permissions.canEdit && (
            <Button variant="outline" size="sm">
              <Edit className="mr-2 h-4 w-4" />
              Edit Project
            </Button>
          )}

          <Button variant="outline" size="sm">
            <MessageSquare className="mr-2 h-4 w-4" />
            Messages
          </Button>

          {permissions.canViewFinancials && (
            <Button variant="outline" size="sm">
              <DollarSign className="mr-2 h-4 w-4" />
              Finances
            </Button>
          )}

          <Button size="sm">
            <Plus className="mr-2 h-4 w-4" />
            {userRole === "client" ? "Request Update" : "Add Task"}
          </Button>
        </div>
      </div>

      {/* Key Metrics Dashboard */}
      {projectMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Progress Card */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Progress</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {project.completionPercentage || 0}%
              </div>
              <Progress
                value={project.completionPercentage || 0}
                className="mt-2"
              />
              <p className="text-xs text-muted-foreground mt-1">
                {project.completedTasks || 0} of {project.totalTasks || 0} tasks
                completed
              </p>
            </CardContent>
          </Card>

          {/* Timeline Card */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Timeline</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {projectMetrics.timeline.daysRemaining !== null
                  ? projectMetrics.timeline.isOverdue
                    ? `${Math.abs(projectMetrics.timeline.daysRemaining)}d overdue`
                    : `${projectMetrics.timeline.daysRemaining}d left`
                  : "No end date"}
              </div>
              {projectMetrics.timeline.totalProjectDays && (
                <Progress
                  value={projectMetrics.timeline.timelineProgress}
                  className="mt-2"
                />
              )}
              <p className="text-xs text-muted-foreground mt-1">
                {projectMetrics.timeline.daysElapsed || 0} days elapsed
              </p>
            </CardContent>
          </Card>

          {/* Budget Card */}
          {permissions.canViewFinancials && (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Budget</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatAmount({
                    amount: projectMetrics.financial.remainingBudget,
                    currency: "USD",
                  })}
                </div>
                <Progress
                  value={Math.min(
                    projectMetrics.financial.budgetUtilization,
                    100,
                  )}
                  className="mt-2"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  {projectMetrics.financial.budgetUtilization.toFixed(1)}% used
                </p>
              </CardContent>
            </Card>
          )}

          {/* Team Card */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Team</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{project.teamSize || 0}</div>
              <div className="flex items-center mt-2">
                {projectTeam?.slice(0, 3).map((member) => (
                  <Avatar
                    key={member.id}
                    className="h-6 w-6 -ml-1 first:ml-0 border-2 border-background"
                  >
                    <AvatarImage src={member.user.avatarUrl} />
                    <AvatarFallback className="text-xs">
                      {member.user.fullName
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                ))}
                {(project.teamSize || 0) > 3 && (
                  <span className="text-xs text-muted-foreground ml-2">
                    +{(project.teamSize || 0) - 3} more
                  </span>
                )}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Active members
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content Tabs */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <TabsList className="grid w-full grid-cols-2 lg:grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="tasks">Tasks</TabsTrigger>
          <TabsTrigger value="team">Team</TabsTrigger>
          {permissions.canViewFinancials && (
            <TabsTrigger value="financial">Financial</TabsTrigger>
          )}
          <TabsTrigger value="communication">Updates</TabsTrigger>
          <TabsTrigger value="resources">Resources</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                {project.recentActivity ? (
                  <div className="space-y-3">
                    <p className="text-sm">{project.recentActivity}</p>
                    <Separator />
                    <Button variant="outline" size="sm" className="w-full">
                      <Eye className="mr-2 h-4 w-4" />
                      View All Activity
                    </Button>
                  </div>
                ) : (
                  <p className="text-muted-foreground text-sm">
                    No recent activity
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Project Phases */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Project Phases
                </CardTitle>
              </CardHeader>
              <CardContent>
                {projectPhases && projectPhases.length > 0 ? (
                  <div className="space-y-3">
                    {projectPhases.slice(0, 3).map((phase) => (
                      <div
                        key={phase.id}
                        className="flex items-center justify-between"
                      >
                        <div className="flex items-center gap-2">
                          <div
                            className={cn(
                              "w-2 h-2 rounded-full",
                              phase.status === "completed"
                                ? "bg-green-500"
                                : phase.status === "in_progress"
                                  ? "bg-blue-500"
                                  : "bg-gray-300",
                            )}
                          />
                          <span className="text-sm font-medium">
                            {phase.name}
                          </span>
                        </div>
                        <span className="text-xs text-muted-foreground">
                          {phase.completionPercentage || 0}%
                        </span>
                      </div>
                    ))}
                    {projectPhases.length > 3 && (
                      <Button variant="outline" size="sm" className="w-full">
                        View All {projectPhases.length} Phases
                      </Button>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <p className="text-muted-foreground text-sm mb-2">
                      No phases created yet
                    </p>
                    {permissions.canEdit && (
                      <Button variant="outline" size="sm">
                        <Plus className="mr-2 h-4 w-4" />
                        Create First Phase
                      </Button>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Task Status Overview */}
          {project.totalTasks > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5" />
                  Task Status Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {project.completedTasks || 0}
                    </div>
                    <p className="text-sm text-muted-foreground">Completed</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {project.inProgressTasks || 0}
                    </div>
                    <p className="text-sm text-muted-foreground">In Progress</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">
                      {(project.totalTasks || 0) -
                        (project.completedTasks || 0) -
                        (project.inProgressTasks || 0)}
                    </div>
                    <p className="text-sm text-muted-foreground">Pending</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">
                      {project.overdueTasks || 0}
                    </div>
                    <p className="text-sm text-muted-foreground">Overdue</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Tasks Tab */}
        <TabsContent value="tasks">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Project Tasks</CardTitle>
              {permissions.canCreateTasks && (
                <Button size="sm">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Task
                </Button>
              )}
            </CardHeader>
            <CardContent>
              {projectTasks && projectTasks.length > 0 ? (
                <div className="space-y-4">
                  {projectTasks.slice(0, 5).map((task) => (
                    <div
                      key={task.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <div
                          className={cn(
                            "w-2 h-2 rounded-full",
                            task.status === "completed"
                              ? "bg-green-500"
                              : task.status === "in_progress"
                                ? "bg-blue-500"
                                : task.status === "blocked"
                                  ? "bg-red-500"
                                  : "bg-gray-300",
                          )}
                        />
                        <div>
                          <p className="font-medium">{task.name}</p>
                          {task.assignedUser && (
                            <p className="text-sm text-muted-foreground">
                              Assigned to: {task.assignedUser.fullName}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {task.priority && (
                          <Badge variant="outline" className="capitalize">
                            {task.priority}
                          </Badge>
                        )}
                        <span className="text-sm text-muted-foreground">
                          {task.completionPercentage || 0}%
                        </span>
                      </div>
                    </div>
                  ))}
                  {projectTasks.length > 5 && (
                    <Button variant="outline" className="w-full">
                      View All {projectTasks.length} Tasks
                    </Button>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Clipboard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No tasks yet</h3>
                  <p className="text-muted-foreground mb-4">
                    Get started by creating your first task
                  </p>
                  {permissions.canCreateTasks && (
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      Create First Task
                    </Button>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Team Tab */}
        <TabsContent value="team">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Project Team</CardTitle>
              {permissions.canManageTeam && (
                <Button size="sm">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Member
                </Button>
              )}
            </CardHeader>
            <CardContent>
              {projectTeam && projectTeam.length > 0 ? (
                <div className="space-y-4">
                  {projectTeam.map((member) => (
                    <div
                      key={member.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarImage src={member.user.avatarUrl} />
                          <AvatarFallback>
                            {member.user.fullName
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{member.user.fullName}</p>
                          <p className="text-sm text-muted-foreground">
                            {member.user.email}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="capitalize">
                          {member.role.replace("_", " ")}
                        </Badge>
                        {member.hourlyRate && permissions.canViewFinancials && (
                          <span className="text-sm text-muted-foreground">
                            ${member.hourlyRate}/hr
                          </span>
                        )}
                        <div className="flex gap-1">
                          <Button variant="ghost" size="sm">
                            <Phone className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Mail className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No team members</h3>
                  <p className="text-muted-foreground mb-4">
                    Add team members to collaborate on this project
                  </p>
                  {permissions.canManageTeam && (
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      Add First Member
                    </Button>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Financial Tab */}
        {permissions.canViewFinancials && (
          <TabsContent value="financial">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Budget Overview */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    Budget Overview
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {project.budget && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Total Budget:</span>
                        <span className="font-medium">
                          {formatAmount({
                            amount: project.budget,
                            currency: "USD",
                          })}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Spent:</span>
                        <span className="font-medium">
                          {formatAmount({
                            amount: project.actualCost || 0,
                            currency: "USD",
                          })}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Remaining:</span>
                        <span className="font-medium">
                          {formatAmount({
                            amount:
                              projectMetrics?.financial.remainingBudget || 0,
                            currency: "USD",
                          })}
                        </span>
                      </div>
                      <Progress
                        value={projectMetrics?.financial.budgetUtilization || 0}
                        className="mt-2"
                      />
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Cost Breakdown */}
              <Card>
                <CardHeader>
                  <CardTitle>Cost Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span>Labor:</span>
                      <span className="font-medium">$0</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Materials:</span>
                      <span className="font-medium">$0</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Equipment:</span>
                      <span className="font-medium">$0</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Other:</span>
                      <span className="font-medium">$0</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        )}

        {/* Communication Tab */}
        <TabsContent value="communication">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Project Updates</CardTitle>
              <Button size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Send Update
              </Button>
            </CardHeader>
            <CardContent>
              {projectCommunications && projectCommunications.length > 0 ? (
                <div className="space-y-4">
                  {projectCommunications.slice(0, 5).map((comm) => (
                    <div key={comm.id} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <Avatar className="h-6 w-6">
                            <AvatarImage src={comm.sender.avatarUrl} />
                            <AvatarFallback className="text-xs">
                              {comm.sender.fullName
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-sm font-medium">
                            {comm.sender.fullName}
                          </span>
                        </div>
                        <span className="text-xs text-muted-foreground">
                          {format(new Date(comm.createdAt), "MMM dd, HH:mm")}
                        </span>
                      </div>
                      {comm.subject && (
                        <p className="text-sm font-medium mb-1">
                          {comm.subject}
                        </p>
                      )}
                      <p className="text-sm text-muted-foreground">
                        {comm.content}
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    No communications yet
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    Start communicating with your team
                  </p>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Send First Message
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Resources Tab */}
        <TabsContent value="resources">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Project Resources</CardTitle>
              {permissions.canManageResources && (
                <Button size="sm">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Resource
                </Button>
              )}
            </CardHeader>
            <CardContent>
              {projectResources && projectResources.length > 0 ? (
                <div className="space-y-4">
                  {projectResources.map((resource) => (
                    <div
                      key={resource.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <div
                          className={cn(
                            "w-8 h-8 rounded-full flex items-center justify-center",
                            resource.resourceType === "labor"
                              ? "bg-blue-100 text-blue-600"
                              : resource.resourceType === "equipment"
                                ? "bg-green-100 text-green-600"
                                : resource.resourceType === "material"
                                  ? "bg-orange-100 text-orange-600"
                                  : resource.resourceType === "subcontractor"
                                    ? "bg-purple-100 text-purple-600"
                                    : "bg-gray-100 text-gray-600",
                          )}
                        >
                          {resource.resourceType === "labor" ? (
                            <Users className="h-4 w-4" />
                          ) : resource.resourceType === "equipment" ? (
                            <Wrench className="h-4 w-4" />
                          ) : resource.resourceType === "material" ? (
                            <Building2 className="h-4 w-4" />
                          ) : resource.resourceType === "subcontractor" ? (
                            <Shield className="h-4 w-4" />
                          ) : (
                            <FileText className="h-4 w-4" />
                          )}
                        </div>
                        <div>
                          <p className="font-medium">{resource.name}</p>
                          <p className="text-sm text-muted-foreground capitalize">
                            {resource.resourceType} • Qty: {resource.quantity}{" "}
                            {resource.unit || ""}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        {resource.totalCost &&
                          permissions.canViewFinancials && (
                            <p className="font-medium">
                              {formatAmount({
                                amount: resource.totalCost,
                                currency: "USD",
                              })}
                            </p>
                          )}
                        {resource.unitCost && permissions.canViewFinancials && (
                          <p className="text-sm text-muted-foreground">
                            {formatAmount({
                              amount: resource.unitCost,
                              currency: "USD",
                            })}{" "}
                            per {resource.unit || "unit"}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Wrench className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    No resources added
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    Add resources to track materials, equipment, and labor
                  </p>
                  {permissions.canManageResources && (
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      Add First Resource
                    </Button>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
