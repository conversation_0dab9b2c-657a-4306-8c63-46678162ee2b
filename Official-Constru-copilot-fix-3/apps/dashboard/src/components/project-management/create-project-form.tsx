"use client";

import { useTR<PERSON> } from "@/trpc/client";
import { Badge } from "@constru/ui/badge";
import { <PERSON><PERSON> } from "@constru/ui/button";
import { Calendar } from "@constru/ui/calendar";
import { Card, CardContent, CardHeader, CardTitle } from "@constru/ui/card";
import { cn } from "@constru/ui/cn";
import { Input } from "@constru/ui/input";
import { Label } from "@constru/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@constru/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@constru/ui/select";
import { Separator } from "@constru/ui/separator";
import { Textarea } from "@constru/ui/textarea";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import {
  Building2,
  CalendarIcon,
  DollarSign,
  FileText,
  Loader2,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const createProjectSchema = z.object({
  name: z.string().min(1, "Project name is required"),
  description: z.string().optional(),
  projectType: z.enum([
    "residential_construction",
    "commercial_construction",
    "renovation",
    "infrastructure",
    "maintenance",
    "custom",
  ]),
  priority: z.enum(["low", "medium", "high", "critical"]).default("medium"),
  customerId: z.string().optional(),
  projectManagerId: z.string().optional(),
  budget: z.number().positive().optional(),
  currency: z.string().default("USD"),
  plannedStartDate: z.date().optional(),
  plannedEndDate: z.date().optional(),
  locationAddress: z.string().optional(),
  weatherDependent: z.boolean().default(false),
  permitRequired: z.boolean().default(false),
});

type CreateProjectForm = z.infer<typeof createProjectSchema>;

interface CreateProjectFormProps {
  template?: {
    id: string;
    name: string;
    description: string;
    estimatedDuration: string;
    phases: string[];
  };
  onSuccess: () => void;
  onCancel: () => void;
}

export function CreateProjectForm({
  template,
  onSuccess,
  onCancel,
}: CreateProjectFormProps) {
  const trpc = useTRPC();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<CreateProjectForm>({
    resolver: zodResolver(createProjectSchema),
    defaultValues: {
      projectType:
        template?.id === "residential"
          ? "residential_construction"
          : template?.id === "commercial"
            ? "commercial_construction"
            : template?.id === "renovation"
              ? "renovation"
              : template?.id === "infrastructure"
                ? "infrastructure"
                : "custom",
      priority: "medium",
      currency: "USD",
      weatherDependent: false,
      permitRequired: false,
    },
  });

  // Fetch customers and team members
  const { data: customers } = trpc.customers.get.useQuery({ pageSize: 100 });
  const { data: team } = trpc.team.current.useQuery();

  const createProjectMutation = trpc.trackerProjects.upsert.useMutation({
    onSuccess: () => {
      onSuccess();
    },
    onError: (error) => {
      console.error("Failed to create project:", error);
    },
  });

  const onSubmit = async (data: CreateProjectForm) => {
    setIsSubmitting(true);
    try {
      await createProjectMutation.mutateAsync({
        name: data.name,
        description: data.description,
        // Map project type to the existing schema if needed
        customerId: data.customerId,
        // Add other fields as needed
      });
    } catch (error) {
      console.error("Error creating project:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      {/* Template Info */}
      {template && template.id !== "custom" && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              {template.name} Template
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <p className="text-sm text-muted-foreground">
              {template.description}
            </p>
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-1">
                <CalendarIcon className="h-3 w-3" />
                <span>Duration: {template.estimatedDuration}</span>
              </div>
              {template.phases.length > 0 && (
                <div className="flex items-center gap-1">
                  <FileText className="h-3 w-3" />
                  <span>{template.phases.length} phases included</span>
                </div>
              )}
            </div>
            {template.phases.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {template.phases.map((phase) => (
                  <Badge key={phase} variant="secondary" className="text-xs">
                    {phase}
                  </Badge>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Basic Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Project Name *</Label>
              <Input
                id="name"
                {...form.register("name")}
                placeholder="Enter project name"
              />
              {form.formState.errors.name && (
                <p className="text-sm text-red-600">
                  {form.formState.errors.name.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="projectType">Project Type</Label>
              <Select
                value={form.watch("projectType")}
                onValueChange={(value) =>
                  form.setValue("projectType", value as any)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select project type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="residential_construction">
                    Residential Construction
                  </SelectItem>
                  <SelectItem value="commercial_construction">
                    Commercial Construction
                  </SelectItem>
                  <SelectItem value="renovation">Renovation</SelectItem>
                  <SelectItem value="infrastructure">Infrastructure</SelectItem>
                  <SelectItem value="maintenance">Maintenance</SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...form.register("description")}
              placeholder="Enter project description"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="priority">Priority</Label>
              <Select
                value={form.watch("priority")}
                onValueChange={(value) =>
                  form.setValue("priority", value as any)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="customer">Customer</Label>
              <Select
                value={form.watch("customerId") || ""}
                onValueChange={(value) => form.setValue("customerId", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select customer" />
                </SelectTrigger>
                <SelectContent>
                  {customers?.data?.map((customer) => (
                    <SelectItem key={customer.id} value={customer.id}>
                      {customer.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Financial Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Financial Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="budget">Budget</Label>
              <Input
                id="budget"
                type="number"
                {...form.register("budget", { valueAsNumber: true })}
                placeholder="Enter project budget"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="currency">Currency</Label>
              <Select
                value={form.watch("currency")}
                onValueChange={(value) => form.setValue("currency", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="USD">USD ($)</SelectItem>
                  <SelectItem value="EUR">EUR (€)</SelectItem>
                  <SelectItem value="GBP">GBP (£)</SelectItem>
                  <SelectItem value="CAD">CAD ($)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Location */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            Location & Timeline
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="location">Project Location</Label>
            <Input
              id="location"
              {...form.register("locationAddress")}
              placeholder="Enter project address"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Planned Start Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "justify-start text-left font-normal",
                      !form.watch("plannedStartDate") &&
                        "text-muted-foreground",
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {form.watch("plannedStartDate") ? (
                      format(form.watch("plannedStartDate")!, "PPP")
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={form.watch("plannedStartDate")}
                    onSelect={(date) => form.setValue("plannedStartDate", date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label>Planned End Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "justify-start text-left font-normal",
                      !form.watch("plannedEndDate") && "text-muted-foreground",
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {form.watch("plannedEndDate") ? (
                      format(form.watch("plannedEndDate")!, "PPP")
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={form.watch("plannedEndDate")}
                    onSelect={(date) => form.setValue("plannedEndDate", date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex items-center justify-end gap-4 pt-6 border-t">
        <Button variant="outline" onClick={onCancel} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Create Project
        </Button>
      </div>
    </form>
  );
}
