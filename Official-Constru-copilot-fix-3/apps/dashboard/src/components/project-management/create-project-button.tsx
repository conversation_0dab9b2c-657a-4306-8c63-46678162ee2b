"use client";

import { <PERSON><PERSON> } from "@constru/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@constru/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@constru/ui/dropdown-menu";
import {
  Building2,
  ChevronDown,
  FileText,
  Home,
  Plus,
  Settings,
  Wrench,
} from "lucide-react";
import { useState } from "react";
import { CreateProjectForm } from "./create-project-form";

const projectTemplates = [
  {
    id: "residential",
    name: "Residential Construction",
    description: "Single/multi-family homes, apartments",
    icon: Home,
    estimatedDuration: "4-12 months",
    phases: [
      "Foundation",
      "Framing",
      "Roofing",
      "Utilities",
      "Interior",
      "Finishing",
    ],
  },
  {
    id: "commercial",
    name: "Commercial Construction",
    description: "Office buildings, retail spaces, warehouses",
    icon: Building2,
    estimatedDuration: "6-24 months",
    phases: [
      "Site Prep",
      "Foundation",
      "Structure",
      "Envelope",
      "Systems",
      "Interior",
      "Commissioning",
    ],
  },
  {
    id: "renovation",
    name: "Renovation Project",
    description: "Remodeling and renovation work",
    icon: Wrench,
    estimatedDuration: "1-6 months",
    phases: [
      "Planning",
      "Demolition",
      "Structural",
      "Systems",
      "Finishes",
      "Final",
    ],
  },
  {
    id: "infrastructure",
    name: "Infrastructure Project",
    description: "Roads, bridges, utilities",
    icon: Settings,
    estimatedDuration: "12-36 months",
    phases: [
      "Design",
      "Permits",
      "Site Prep",
      "Construction",
      "Testing",
      "Handover",
    ],
  },
  {
    id: "custom",
    name: "Custom Project",
    description: "Start from scratch with custom setup",
    icon: FileText,
    estimatedDuration: "Variable",
    phases: [],
  },
];

export function CreateProjectButton() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);

  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId);
    setIsDialogOpen(true);
  };

  const handleQuickCreate = () => {
    setSelectedTemplate("custom");
    setIsDialogOpen(true);
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button className="gap-2">
            <Plus className="h-4 w-4" />
            New Project
            <ChevronDown className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-80">
          <DropdownMenuLabel>Choose Project Type</DropdownMenuLabel>
          <DropdownMenuSeparator />

          {projectTemplates.map((template) => {
            const Icon = template.icon;
            return (
              <DropdownMenuItem
                key={template.id}
                onClick={() => handleTemplateSelect(template.id)}
                className="flex flex-col items-start gap-2 p-4 cursor-pointer"
              >
                <div className="flex items-center gap-3 w-full">
                  <div className="p-2 bg-blue-50 rounded-md">
                    <Icon className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium">{template.name}</div>
                    <div className="text-xs text-muted-foreground line-clamp-1">
                      {template.description}
                    </div>
                  </div>
                </div>
                <div className="text-xs text-muted-foreground ml-11">
                  Duration: {template.estimatedDuration}
                  {template.phases.length > 0 &&
                    ` • ${template.phases.length} phases`}
                </div>
              </DropdownMenuItem>
            );
          })}

          <DropdownMenuSeparator />

          <DropdownMenuItem onClick={handleQuickCreate} className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gray-50 rounded-md">
                <Plus className="h-4 w-4 text-gray-600" />
              </div>
              <div>
                <div className="font-medium">Quick Create</div>
                <div className="text-xs text-muted-foreground">
                  Create a basic project without template
                </div>
              </div>
            </div>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              Create New Project
              {selectedTemplate && selectedTemplate !== "custom" && (
                <span className="text-base font-normal text-muted-foreground ml-2">
                  (
                  {
                    projectTemplates.find((t) => t.id === selectedTemplate)
                      ?.name
                  }
                  )
                </span>
              )}
            </DialogTitle>
            <DialogDescription>
              {selectedTemplate && selectedTemplate !== "custom"
                ? `Set up a new ${projectTemplates.find((t) => t.id === selectedTemplate)?.name.toLowerCase()} with pre-configured phases and tasks.`
                : "Create a custom project with your own configuration."}
            </DialogDescription>
          </DialogHeader>

          <CreateProjectForm
            template={
              selectedTemplate
                ? projectTemplates.find((t) => t.id === selectedTemplate)
                : undefined
            }
            onSuccess={() => setIsDialogOpen(false)}
            onCancel={() => setIsDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </>
  );
}
