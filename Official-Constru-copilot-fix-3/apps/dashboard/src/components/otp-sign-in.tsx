"use client";

import { verifyOtpAction } from "@/actions/verify-otp-action";
import { createClient } from "@constru/supabase/client";
import { cn } from "@constru/ui/cn";
import { Form, FormControl, FormField, FormItem } from "@constru/ui/form";
import { Input } from "@constru/ui/input";
import { InputOTP, InputOTPGroup, InputOTPSlot } from "@constru/ui/input-otp";
import { SubmitButton } from "@constru/ui/submit-button";
import { zodResolver } from "@hookform/resolvers/zod";
import { useAction } from "next-safe-action/hooks";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const formSchema = z.object({
  email: z.string().email(),
});

type Props = {
  className?: string;
};

export function OTPSignIn({ className }: Props) {
  const verifyOtp = useAction(verifyOtpAction);
  const [isLoading, setLoading] = useState(false);
  const [isSent, setSent] = useState(false);
  const [email, setEmail] = useState<string>();
  const [error, setError] = useState<string | null>(null);
  const [isVerifying, setIsVerifying] = useState(false);
  const supabase = createClient();
  const router = useRouter();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
    },
  });

  // Listen for auth state changes
  useEffect(() => {
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === "SIGNED_IN" && session && isVerifying) {
        console.log("Auth state changed to SIGNED_IN, checking user setup...");

        try {
          // Check if user has teams
          const { count } = await supabase
            .from("users_on_team")
            .select("*", { count: "exact" })
            .eq("user_id", session.user.id);

          if (count === 0) {
            // No teams, redirect to team creation
            console.log("No teams found, redirecting to team creation...");
            window.location.href = "/en/teams/create";
            return;
          }

          // Check if user has completed setup (full_name)
          if (!session.user.user_metadata?.full_name) {
            console.log("User setup incomplete, redirecting to setup...");
            window.location.href = "/en/setup";
            return;
          }

          // All checks passed, redirect to dashboard
          console.log("User setup complete, redirecting to dashboard...");
          window.location.href = "/en";
        } catch (error) {
          console.error("Error checking user setup:", error);
          // Fallback to dashboard
          window.location.href = "/en";
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [supabase.auth, isVerifying]);

  // Handle successful OTP verification
  useEffect(() => {
    if (verifyOtp.result.data?.success) {
      // Force a session refresh and ensure it's properly established
      const handleSuccessfulVerification = async () => {
        try {
          // Refresh the session to ensure it's established
          await supabase.auth.getSession();

          // Wait a bit for session to be properly set
          await new Promise((resolve) => setTimeout(resolve, 500));

          // Check if session is actually established
          const {
            data: { session },
          } = await supabase.auth.getSession();

          if (session) {
            // Session is confirmed, check user setup
            console.log("Session established, checking user setup...");

            try {
              // Check if user has teams
              const { count } = await supabase
                .from("users_on_team")
                .select("*", { count: "exact" })
                .eq("user_id", session.user.id);

              if (count === 0) {
                // No teams, redirect to team creation
                console.log("No teams found, redirecting to team creation...");
                window.location.href = "/en/teams/create";
                return;
              }

              // Check if user has completed setup (full_name)
              if (!session.user.user_metadata?.full_name) {
                console.log("User setup incomplete, redirecting to setup...");
                window.location.href = "/en/setup";
                return;
              }

              // All checks passed, redirect to dashboard
              console.log("User setup complete, redirecting to dashboard...");
              window.location.href = "/en";
            } catch (error) {
              console.error("Error checking user setup:", error);
              // Fallback to dashboard
              window.location.href = "/en";
            }
          } else {
            // Session not established, try one more time
            await new Promise((resolve) => setTimeout(resolve, 1000));
            const {
              data: { session: retrySession },
            } = await supabase.auth.getSession();

            if (retrySession) {
              // Retry the setup check with the new session
              try {
                const { count } = await supabase
                  .from("users_on_team")
                  .select("*", { count: "exact" })
                  .eq("user_id", retrySession.user.id);

                if (count === 0) {
                  window.location.href = "/en/teams/create";
                  return;
                }

                if (!retrySession.user.user_metadata?.full_name) {
                  window.location.href = "/en/setup";
                  return;
                }

                window.location.href = "/en";
              } catch (error) {
                console.error("Error in retry setup check:", error);
                window.location.href = "/en";
              }
            } else {
              setError(
                "Authentication successful but session not established. Please try signing in again.",
              );
              setIsVerifying(false);
            }
          }
        } catch (error) {
          console.error("Error establishing session:", error);
          setError("Failed to establish session. Please try signing in again.");
          setIsVerifying(false);
        }
      };

      handleSuccessfulVerification();
    }
  }, [verifyOtp.result.data?.success, supabase.auth]);

  // Handle errors
  useEffect(() => {
    if (verifyOtp.result.serverError) {
      setError(verifyOtp.result.serverError);
      setIsVerifying(false);
    }
  }, [verifyOtp.result.serverError]);

  async function onSubmit({ email }: z.infer<typeof formSchema>) {
    setLoading(true);
    setError(null);

    setEmail(email);

    const { data, error } = await supabase.auth.signInWithOtp({ email });

    if (error) {
      console.error("Error sending OTP:", error);
      setError("Failed to send OTP. Please try again.");
      setLoading(false);
      return;
    }

    console.log("OTP sent successfully to:", email);

    setSent(true);
    setLoading(false);
  }

  async function onComplete(token: string) {
    if (!email) return;

    setError(null);
    setIsVerifying(true);

    try {
      verifyOtp.execute({
        token,
        email,
      });
    } catch (error) {
      console.error("Error during OTP verification:", error);
      setError("Failed to verify OTP. Please try again.");
      setIsVerifying(false);
    }
  }

  if (isSent) {
    return (
      <div className={cn("flex flex-col space-y-4 items-center", className)}>
        <InputOTP
          maxLength={6}
          autoFocus
          onComplete={onComplete}
          disabled={verifyOtp.status === "executing"}
          render={({ slots }) => (
            <InputOTPGroup>
              {slots.map((slot, index) => (
                <InputOTPSlot
                  key={index.toString()}
                  {...slot}
                  className="w-[62px] h-[62px]"
                />
              ))}
            </InputOTPGroup>
          )}
        />

        {error && (
          <div className="text-sm text-red-500 text-center">{error}</div>
        )}

        {verifyOtp.status === "executing" && (
          <div className="text-sm text-gray-500">Verifying code...</div>
        )}

        <div className="flex space-x-2">
          <span className="text-sm text-[#878787]">
            Didn't receive the email?
          </span>
          <button
            onClick={() => {
              setSent(false);
              setError(null);
              setIsVerifying(false);
              verifyOtp.reset();
            }}
            type="button"
            className="text-sm text-primary underline font-medium"
            disabled={verifyOtp.status === "executing"}
          >
            Resend code
          </button>
        </div>
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className={cn("flex flex-col space-y-4", className)}>
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    placeholder="Enter email address"
                    {...field}
                    autoCapitalize="false"
                    autoCorrect="false"
                    spellCheck="false"
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <SubmitButton
            type="submit"
            className="bg-primary px-6 py-4 text-secondary font-medium flex space-x-2 h-[40px] w-full"
            isSubmitting={isLoading}
          >
            Continue
          </SubmitButton>
        </div>
      </form>
    </Form>
  );
}
