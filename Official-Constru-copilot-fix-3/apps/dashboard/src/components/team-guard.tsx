"use client";

import { useUserQuery } from "@/hooks/use-user";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

interface TeamGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function TeamGuard({ children, fallback }: TeamGuardProps) {
  const router = useRouter();

  const {
    data: user,
    isLoading,
    error,
  } = useUserQuery();

  useEffect(() => {
    // If user has no team, redirect to setup
    if (!isLoading && user && !user.teamId) {
      router.replace("/setup");
    }
  }, [user, isLoading, router]);

  // Handle TRPC errors gracefully
  if (error) {
    const trpcError = error as any;
    if (
      trpcError?.message === "NO_TEAM_ASSIGNED" ||
      trpcError?.message === "No permission to access this team" ||
      trpcError?.code === "FORBIDDEN" ||
      trpcError?.code === "PRECONDITION_FAILED"
    ) {
      router.replace("/setup");
      return null;
    }
    // For other errors, show fallback or error state
    console.warn("TeamGuard error:", trpcError);
    return (
      fallback || (
        <div>
          Error loading user data.{" "}
          <button onClick={() => window.location.reload()}>Retry</button>
        </div>
      )
    );
  }

  if (isLoading) {
    return fallback || null;
  }

  // Only render children if user has a team
  if (user?.teamId) {
    return <>{children}</>;
  }

  return null;
}
