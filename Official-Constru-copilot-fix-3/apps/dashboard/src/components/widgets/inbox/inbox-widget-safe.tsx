"use client";

import { useTR<PERSON> } from "@/trpc/client";
import { Skeleton } from "@constru/ui/skeleton";
import { useQuery } from "@tanstack/react-query";
import { InboxWidget } from "./inbox-widget";

export function InboxWidgetSafe() {
  const trpc = useTRPC();

  // Use regular useQuery instead of useSuspenseQuery to handle errors gracefully
  const { data, isLoading, error } = useQuery(trpc.inbox.get.queryOptions());

  if (error) {
    return (
      <div className="flex items-center justify-center h-full text-sm text-[#606060]">
        <p>Unable to load inbox</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-3 p-4">
        <Skeleton className="h-4 w-[200px]" />
        <Skeleton className="h-4 w-[150px]" />
      </div>
    );
  }

  return <InboxWidget />;
}
