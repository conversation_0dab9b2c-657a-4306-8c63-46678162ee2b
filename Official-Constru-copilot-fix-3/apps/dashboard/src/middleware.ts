import { updateSession } from "@constru/supabase/middleware";
import { createClient } from "@constru/supabase/server";
import { createI18nMiddleware } from "next-international/middleware";
import { type NextRequest, NextResponse } from "next/server";

const I18nMiddleware = createI18nMiddleware({
  locales: ["en"],
  defaultLocale: "en",
  urlMappingStrategy: "rewrite",
});

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // Skip middleware for static files and API routes
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/api/') ||
    pathname.includes('.') ||
    pathname === '/favicon.ico'
  ) {
    return NextResponse.next();
  }

  // Handle i18n first
  const i18nResponse = I18nMiddleware(request);

  // Update session with the i18n response
  const response = await updateSession(request, i18nResponse);

  // Extract locale from pathname
  const pathnameLocale = pathname.split("/")[1];
  const isValidLocale = ["en"].includes(pathnameLocale);

  // Remove the locale from the pathname if it exists
  const pathnameWithoutLocale = isValidLocale
    ? pathname.slice(pathnameLocale.length + 1) || "/"
    : pathname;

  try {
    const supabase = await createClient();
    const {
      data: { session },
    } = await supabase.auth.getSession();

    // 1. Not authenticated - redirect to login
    if (
      !session &&
      pathnameWithoutLocale !== "/login" &&
      !pathnameWithoutLocale.includes("/i/") &&
      !pathnameWithoutLocale.includes("/all-done")
    ) {
      const returnTo = pathnameWithoutLocale !== "/" ? pathnameWithoutLocale : "";
      const loginUrl = new URL("/en/login", request.url);

      if (returnTo && request.nextUrl.search) {
        loginUrl.searchParams.set("return_to", `${returnTo}${request.nextUrl.search}`);
      } else if (returnTo) {
        loginUrl.searchParams.set("return_to", returnTo);
      } else if (request.nextUrl.search) {
        loginUrl.searchParams.set("return_to", request.nextUrl.search);
      }

      return NextResponse.redirect(loginUrl);
    }

    // If authenticated, proceed with other checks
    if (session) {
      // 2. Check user setup (full_name)
      if (
        pathnameWithoutLocale !== "/setup" &&
        pathnameWithoutLocale !== "/teams/create" &&
        pathnameWithoutLocale !== "/teams" &&
        !pathnameWithoutLocale.startsWith("/teams/invite/") &&
        !session?.user?.user_metadata?.full_name
      ) {
        // Redirect to setup if user hasn't completed setup
        const setupUrl = new URL("/en/setup", request.url);
        return NextResponse.redirect(setupUrl);
      }

      // 3. Check MFA Verification
      const { data: mfaData } =
        await supabase.auth.mfa.getAuthenticatorAssuranceLevel();
      if (
        mfaData &&
        mfaData.nextLevel === "aal2" &&
        mfaData.nextLevel !== mfaData.currentLevel &&
        pathnameWithoutLocale !== "/mfa/verify"
      ) {
        // Redirect to MFA verification if needed and not already there
        const mfaUrl = new URL("/en/mfa/verify", request.url);
        return NextResponse.redirect(mfaUrl);
      }
    }

    // If all checks pass, return the original response
    return response;
  } catch (error) {
    // If there's an error with Supabase, log it and continue
    console.error("Middleware error:", error);
    return response;
  }
}

export const config = {
  matcher: ["/((?!_next/static|_next/image|favicon.ico|api).*)"],
};