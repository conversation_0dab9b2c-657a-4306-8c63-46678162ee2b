import { updateSession } from "@constru/supabase/middleware";
import { createClient } from "@constru/supabase/server";
import { createI18nMiddleware } from "next-international/middleware";
import { type NextRequest, NextResponse } from "next/server";

const I18nMiddleware = createI18nMiddleware({
  locales: ["en"],
  defaultLocale: "en",
  urlMappingStrategy: "rewrite",
});

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // Skip middleware for static files and API routes
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/api/') ||
    pathname.includes('.') ||
    pathname === '/favicon.ico'
  ) {
    return NextResponse.next();
  }

  // Extract locale from pathname
  const pathnameLocale = pathname.split("/")[1];
  const isValidLocale = ["en"].includes(pathnameLocale);

  // Remove the locale from the pathname if it exists
  const pathnameWithoutLocale = isValidLocale
    ? pathname.slice(pathnameLocale.length + 1) || "/"
    : pathname;

  // Handle locale redirects first
  if (!isValidLocale && pathname !== "/") {
    const redirectUrl = new URL(`/en${pathname}`, request.url);
    return NextResponse.redirect(redirectUrl);
  }

  // Create response and update session
  const response = NextResponse.next();
  const sessionResponse = await updateSession(request, response);

  try {
    const supabase = await createClient();
    const {
      data: { session },
    } = await supabase.auth.getSession();

    // 1. Authentication check - redirect to login if not authenticated
    if (
      !session &&
      pathnameWithoutLocale !== "/login" &&
      !pathnameWithoutLocale.includes("/i/") &&
      !pathnameWithoutLocale.includes("/all-done")
    ) {
      const returnTo = pathnameWithoutLocale !== "/" ? pathnameWithoutLocale : "";
      const loginUrl = new URL("/en/login", request.url);

      if (returnTo) {
        loginUrl.searchParams.set("return_to", returnTo);
      }

      return NextResponse.redirect(loginUrl);
    }

    // 2. If authenticated, check user setup and team membership
    if (session) {
      const isSetupPage = pathnameWithoutLocale === "/setup";
      const isTeamsPage = pathnameWithoutLocale === "/teams/create" ||
                         pathnameWithoutLocale === "/teams" ||
                         pathnameWithoutLocale.startsWith("/teams/invite/");

      // Check if user has completed setup
      if (
        !isSetupPage &&
        !isTeamsPage &&
        !session?.user?.user_metadata?.full_name
      ) {
        const setupUrl = new URL("/en/setup", request.url);
        return NextResponse.redirect(setupUrl);
      }

      // Check team membership (skip for setup and teams pages)
      if (!isSetupPage && !isTeamsPage) {
        try {
          const { count } = await supabase
            .from("users_on_team")
            .select("*", { count: "exact" })
            .eq("user_id", session.user.id);

          if (count === 0) {
            const teamsCreateUrl = new URL("/en/teams/create", request.url);
            return NextResponse.redirect(teamsCreateUrl);
          }
        } catch (error) {
          console.warn("Error checking team membership:", error);
          // Don't redirect on error, let the page handle it
        }
      }
    }

    return sessionResponse;
  } catch (error) {
    console.error("Middleware error:", error);
    return sessionResponse;
  }


}

export const config = {
  matcher: ["/((?!_next/static|_next/image|favicon.ico|api).*)"],
};
