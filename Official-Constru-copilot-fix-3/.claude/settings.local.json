{"permissions": {"allow": ["Bash(grep:*)", "Bash(bun run:*)", "Bash(bun lint)", "<PERSON><PERSON>(timeout:*)", "Bash(bun tsc:*)", "Bash(bun db:migrate:*)", "Bash(cp:*)", "Bash(bunx:*)", "Bash(ls:*)", "Bash(find:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./apply-fixes.sh:*)", "Bash(/Users/<USER>/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"ResizeObserver\" search.tsx)", "Bash(/Users/<USER>/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -l \"from \"\"@ui/\" src/)", "Bash(/Users/<USER>/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -l 'from \"\"@ui/' src/)", "Bash(/Users/<USER>/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"AvatarImageNext\" src/)", "Bash(/Users/<USER>/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 5 -B 5 \"from \\\"@constru/ui/avatar\\\"\" /Users/<USER>/Developer/reciepts/Official-Constru-copilot-fix-3/apps/dashboard/src/components/search/search.tsx)", "Bash(/Users/<USER>/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 5 -B 5 '@constru/ui/avatar' /Users/<USER>/Developer/reciepts/Official-Constru-copilot-fix-3/apps/dashboard/src/components/search/search.tsx)", "Bash(/Users/<USER>/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg 'AvatarImage[^N]' /Users/<USER>/Developer/reciepts/Official-Constru-copilot-fix-3/apps/dashboard/src/)", "Bash(rm:*)", "Bash(/Users/<USER>/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"Icons\\.ArrowLeft\" src/)", "Bash(/Users/<USER>/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"Icons\\.ArrowLeft\" src/components/project/project-overview.tsx)", "Bash(/Users/<USER>/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 5 -B 5 \"LogoSmall\\|Logo\\|Overview\\|Transactions\" /Users/<USER>/Developer/reciepts/Official-Constru-copilot-fix-3/packages/ui/src/components/icons.tsx)", "Bash(/Users/<USER>/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"ArrowBack\" /Users/<USER>/Developer/reciepts/Official-Constru-copilot-fix-3/packages/ui/src/components/icons.tsx)", "Bash(/Users/<USER>/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -i \"clock\\|time\" /Users/<USER>/Developer/reciepts/Official-Constru-copilot-fix-3/packages/ui/src/components/icons.tsx)", "Bash(/Users/<USER>/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"Clock\\|Time\" /Users/<USER>/Developer/reciepts/Official-Constru-copilot-fix-3/packages/ui/src/components/icons.tsx)", "Bash(/Users/<USER>/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"Schedule\\|Timer\\|Watch\\|AccessTime\" /Users/<USER>/Developer/reciepts/Official-Constru-copilot-fix-3/packages/ui/src/components/icons.tsx)", "Bash(/Users/<USER>/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"MdAccessTime\\|MdSchedule\\|MdTimer\\|MdWatch\\|MdQueryBuilder\" /Users/<USER>/Developer/reciepts/Official-Constru-copilot-fix-3/packages/ui/src/components/icons.tsx)", "Bash(supabase migration repair:*)", "Bash(supabase migration:*)", "Bash(supabase db:*)", "Bash(psql:*)", "<PERSON><PERSON>(bun dev)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(cat:*)", "Bash(bun:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm run lint)", "Bash(npx biome check:*)", "<PERSON><PERSON>(sed:*)", "Bash(node test-project-management.js)", "Bash(./fix-trpc-imports.sh:*)", "WebFetch(domain:trpc.io)", "Bash(npm run build:*)", "Bash(npm run typecheck:*)", "Bash(./fix-trpc-usage.sh:*)", "Bash(node:*)", "Bash(npm run dev:*)", "Bash(nc:*)", "Bash(TZ=UTC bun run dev)", "Bash(TZ=UTC next dev -p 3001 --turbopack)", "Bash(git checkout:*)", "Bash(git fetch:*)", "Bash(NODE_ENV=development bun run dev 2 >& 1)"], "deny": []}}